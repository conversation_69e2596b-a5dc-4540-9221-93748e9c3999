#!/bin/bash

# 应用程序启动脚本
# 用途：根据不同环境配置启动应用程序
# 支持：local、development、production 环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 获取脚本所在目录的父目录（项目根目录）
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 加载环境配置
load_environment() {
    local env=$1
    local env_file="$PROJECT_ROOT/config/environments/${env}.env"
    
    if [ -f "$env_file" ]; then
        print_info "加载环境配置: $env_file"
        set -a  # 自动导出变量
        source "$env_file"
        set +a
    else
        print_warning "环境配置文件不存在: $env_file"
    fi
}

# 检查基础设施是否就绪
check_infrastructure() {
    local env=$1
    
    print_info "检查基础设施状态..."
    
    case $env in
        "local")
            # 检查本地 Redis 和 PostgreSQL
            if ! nc -z localhost 6379 2>/dev/null; then
                print_error "Redis 未启动 (localhost:6379)"
                print_info "请运行: ./scripts/infrastructure.sh local"
                exit 1
            fi
            
            if ! nc -z localhost 5432 2>/dev/null; then
                print_error "PostgreSQL 未启动 (localhost:5432)"
                print_info "请运行: ./scripts/infrastructure.sh local"
                exit 1
            fi
            
            print_success "本地基础设施就绪"
            ;;
        "development"|"production")
            print_info "容器环境，跳过基础设施检查"
            ;;
    esac
}

# 启动 Silo
start_silo() {
    local env=$1
    
    print_info "启动 Orleans Silo ($env 环境)..."
    
    cd "$PROJECT_ROOT"
    
    case $env in
        "local")
            load_environment "local"
            export ASPNETCORE_ENVIRONMENT=Development
            dotnet run --project src/Curios.Silo &
            SILO_PID=$!
            echo $SILO_PID > /tmp/curios-silo.pid
            ;;
        "development")
            print_info "开发环境 Silo 通过 Docker Compose 管理"
            ;;
        "production")
            print_info "生产环境 Silo 通过 Docker Compose 管理"
            ;;
    esac
}

# 启动 API
start_api() {
    local env=$1
    
    print_info "启动 API 服务 ($env 环境)..."
    
    cd "$PROJECT_ROOT"
    
    case $env in
        "local")
            load_environment "local"
            export ASPNETCORE_ENVIRONMENT=Development
            
            # 等待 Silo 启动
            print_info "等待 Silo 启动..."
            sleep 10
            
            dotnet run --project src/Curios.Api &
            API_PID=$!
            echo $API_PID > /tmp/curios-api.pid
            ;;
        "development")
            print_info "开发环境 API 通过 Docker Compose 管理"
            ;;
        "production")
            print_info "生产环境 API 通过 Docker Compose 管理"
            ;;
    esac
}

# 停止应用程序
stop_app() {
    print_info "停止应用程序..."
    
    # 停止 API
    if [ -f /tmp/curios-api.pid ]; then
        API_PID=$(cat /tmp/curios-api.pid)
        if kill -0 $API_PID 2>/dev/null; then
            print_info "停止 API (PID: $API_PID)"
            kill $API_PID
        fi
        rm -f /tmp/curios-api.pid
    fi
    
    # 停止 Silo
    if [ -f /tmp/curios-silo.pid ]; then
        SILO_PID=$(cat /tmp/curios-silo.pid)
        if kill -0 $SILO_PID 2>/dev/null; then
            print_info "停止 Silo (PID: $SILO_PID)"
            kill $SILO_PID
        fi
        rm -f /tmp/curios-silo.pid
    fi
    
    print_success "应用程序已停止"
}

# 显示应用程序状态
show_status() {
    print_info "应用程序状态："
    
    # 检查 Silo
    if [ -f /tmp/curios-silo.pid ]; then
        SILO_PID=$(cat /tmp/curios-silo.pid)
        if kill -0 $SILO_PID 2>/dev/null; then
            print_success "Silo 运行中 (PID: $SILO_PID)"
        else
            print_error "Silo 未运行"
        fi
    else
        print_error "Silo 未运行"
    fi
    
    # 检查 API
    if [ -f /tmp/curios-api.pid ]; then
        API_PID=$(cat /tmp/curios-api.pid)
        if kill -0 $API_PID 2>/dev/null; then
            print_success "API 运行中 (PID: $API_PID)"
        else
            print_error "API 未运行"
        fi
    else
        print_error "API 未运行"
    fi
}

# 显示访问信息
show_access_info() {
    local env=$1
    
    print_success "应用程序启动完成！"
    echo ""
    
    case $env in
        "local")
            print_info "访问地址："
            echo "  🌐 API:              http://localhost:5314"
            echo "  📖 Swagger:         http://localhost:5314/swagger"
            echo "  ❤️  健康检查:        http://localhost:5314/health"
            echo ""
            print_info "监控地址（如果已启动）："
            echo "  📊 Seq 日志:         http://localhost:5341"
            echo "  🔍 Jaeger 追踪:      http://localhost:16686"
            echo "  📈 Grafana 监控:     http://localhost:3000"
            ;;
        "development")
            print_info "访问地址："
            echo "  🌐 API:              http://localhost"
            echo "  📊 Portainer:        http://localhost:9000"
            ;;
        "production")
            print_info "访问地址："
            echo "  🌐 API:              http://localhost"
            echo "  📊 Portainer:        http://localhost:9000"
            ;;
    esac
}

# 显示帮助信息
show_help() {
    echo "应用程序启动脚本"
    echo ""
    echo "用法: $0 <command> [environment]"
    echo ""
    echo "命令:"
    echo "  start <env>          启动应用程序"
    echo "  stop                 停止应用程序"
    echo "  restart <env>        重启应用程序"
    echo "  status               显示应用程序状态"
    echo "  silo <env>           仅启动 Silo"
    echo "  api <env>            仅启动 API"
    echo ""
    echo "环境:"
    echo "  local                本地开发环境"
    echo "  development          开发环境"
    echo "  production           生产环境"
    echo ""
    echo "示例:"
    echo "  $0 start local       # 启动本地开发环境"
    echo "  $0 silo local        # 仅启动本地 Silo"
    echo "  $0 api local         # 仅启动本地 API"
    echo "  $0 stop              # 停止应用程序"
    echo "  $0 status            # 查看状态"
    echo ""
    echo "注意:"
    echo "  - 启动前请确保基础设施已启动"
    echo "  - 本地环境: ./scripts/infrastructure.sh local"
    echo "  - 开发环境: ./scripts/infrastructure.sh development"
    echo "  - 生产环境: ./scripts/infrastructure.sh production"
}

# 主函数
main() {
    case "${1:-help}" in
        "start")
            local env=${2:-local}
            check_infrastructure "$env"
            start_silo "$env"
            start_api "$env"
            show_access_info "$env"
            ;;
        "stop")
            stop_app
            ;;
        "restart")
            local env=${2:-local}
            stop_app
            sleep 2
            check_infrastructure "$env"
            start_silo "$env"
            start_api "$env"
            show_access_info "$env"
            ;;
        "silo")
            local env=${2:-local}
            check_infrastructure "$env"
            start_silo "$env"
            ;;
        "api")
            local env=${2:-local}
            check_infrastructure "$env"
            start_api "$env"
            ;;
        "status")
            show_status
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            echo "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
