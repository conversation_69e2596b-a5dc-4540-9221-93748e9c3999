#!/bin/bash

# 快速开始脚本 - Docker优先开发模式

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${PURPLE}🚀 $1${NC}"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        echo "安装指南: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose 未安装，请先安装 Docker Compose"
        echo "安装指南: https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    print_success "Docker 环境检查通过"
}

# 检查.NET SDK
check_dotnet() {
    if ! command -v dotnet &> /dev/null; then
        print_warning ".NET SDK 未安装，Aspire调试功能将不可用"
        echo "安装指南: https://dotnet.microsoft.com/download"
        return 1
    fi
    
    # 检查.NET版本
    local dotnet_version=$(dotnet --version)
    print_success ".NET SDK 已安装: $dotnet_version"
    return 0
}

# 启动开发环境
start_development() {
    print_header "启动开发环境"
    
    print_info "切换到部署目录..."
    cd "$(dirname "$0")/../deploy/docker"
    
    print_info "启动开发服务..."
    docker-compose -f docker-compose.dev.yml up -d
    
    print_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    print_info "检查服务状态..."
    docker-compose -f docker-compose.dev.yml ps
}

# 健康检查
health_check() {
    print_header "执行健康检查"
    
    local all_healthy=true
    
    # 检查API健康状态
    print_info "检查API服务..."
    if curl -f -s http://localhost/weatherforecast > /dev/null 2>&1; then
        print_success "API服务正常"
    else
        print_error "API服务异常"
        all_healthy=false
    fi
    
    # 检查数据库连接
    print_info "检查PostgreSQL连接..."
    if docker-compose -f docker-compose.dev.yml exec -T postgres pg_isready -U orleans > /dev/null 2>&1; then
        print_success "PostgreSQL连接正常"
    else
        print_error "PostgreSQL连接异常"
        all_healthy=false
    fi
    
    # 检查Redis连接
    print_info "检查Redis连接..."
    if docker-compose -f docker-compose.dev.yml exec -T redis redis-cli ping > /dev/null 2>&1; then
        print_success "Redis连接正常"
    else
        print_error "Redis连接异常"
        all_healthy=false
    fi
    
    if [ "$all_healthy" = true ]; then
        print_success "所有服务健康检查通过！"
        return 0
    else
        print_error "部分服务健康检查失败"
        return 1
    fi
}

# 显示访问信息
show_access_info() {
    print_header "服务访问信息"
    
    echo ""
    echo "🌐 主要服务："
    echo "   API接口: http://localhost"
    echo "   示例接口: http://localhost/weatherforecast"
    echo ""
    echo "📊 监控命令："
    echo "   查看状态: ./scripts/monitor.sh status"
    echo "   查看日志: ./scripts/monitor.sh logs"
    echo "   健康检查: ./scripts/monitor.sh health"
    echo ""
    echo "🛠️ 管理工具："
    echo "   启动管理界面: ./scripts/monitor.sh tools"
    echo "   然后访问:"
    echo "   - Portainer: http://localhost:9000 (Docker管理)"
    echo "   - PgAdmin: http://localhost:8080 (数据库管理)"
    echo "   - Redis Commander: http://localhost:8081 (Redis管理)"
    echo ""
    echo "🐛 调试模式："
    echo "   Aspire调试: dotnet run --project src/Curios.AppHost"
    echo "   (需要先停止Docker服务: docker-compose down)"
    echo ""
}

# 显示下一步建议
show_next_steps() {
    print_header "下一步建议"
    
    echo ""
    echo "1. 📝 测试API接口:"
    echo "   curl http://localhost/weatherforecast"
    echo ""
    echo "2. 📊 查看服务状态:"
    echo "   ./scripts/monitor.sh status"
    echo ""
    echo "3. 🔍 查看实时日志:"
    echo "   ./scripts/monitor.sh logs"
    echo ""
    echo "4. 🛠️ 启动管理工具:"
    echo "   ./scripts/monitor.sh tools"
    echo ""
    echo "5. 📖 阅读开发指南:"
    echo "   cat DEVELOPMENT_GUIDE.md"
    echo ""
}

# 主函数
main() {
    print_header "Curios API 快速开始"
    echo ""
    
    # 环境检查
    print_info "检查开发环境..."
    check_docker
    
    local dotnet_available=false
    if check_dotnet; then
        dotnet_available=true
    fi
    
    echo ""
    
    # 启动开发环境
    start_development
    
    echo ""
    
    # 健康检查
    if health_check; then
        echo ""
        show_access_info
        show_next_steps
        
        print_success "🎉 开发环境启动成功！"
        
        if [ "$dotnet_available" = true ]; then
            echo ""
            print_info "💡 提示: 如需深度调试，可使用 Aspire 模式"
            echo "   1. 停止Docker服务: docker-compose -f deploy/docker/docker-compose.dev.yml down"
            echo "   2. 启动Aspire: dotnet run --project src/Curios.AppHost"
        fi
    else
        echo ""
        print_error "环境启动过程中出现问题，请检查日志"
        echo ""
        echo "故障排查建议："
        echo "1. 查看服务日志: docker-compose -f deploy/docker/docker-compose.dev.yml logs"
        echo "2. 重启服务: docker-compose -f deploy/docker/docker-compose.dev.yml restart"
        echo "3. 完全重启: docker-compose -f deploy/docker/docker-compose.dev.yml down && docker-compose -f deploy/docker/docker-compose.dev.yml up -d"
        exit 1
    fi
}

# 检查是否在正确的目录
if [ ! -f "Curios.sln" ]; then
    print_error "请在项目根目录中运行此脚本"
    exit 1
fi

main "$@"
