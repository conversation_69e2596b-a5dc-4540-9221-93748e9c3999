#!/bin/bash

# 服务器监控和日志查看脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查是否在正确的目录
check_directory() {
    if [ ! -f "docker-compose.yml" ]; then
        print_error "请在包含 docker-compose.yml 的目录中运行此脚本"
        exit 1
    fi
}

# 显示服务状态
show_status() {
    print_info "显示服务状态..."
    docker-compose ps
    echo ""
    
    print_info "显示容器资源使用情况..."
    docker stats --no-stream
}

# 显示所有日志
show_all_logs() {
    print_info "显示所有服务的实时日志..."
    print_warning "按 Ctrl+C 退出日志查看"
    docker-compose logs -f
}

# 显示特定服务日志
show_service_logs() {
    local service=$1
    
    if [ -z "$service" ]; then
        print_error "请指定服务名称"
        echo "可用服务："
        docker-compose config --services
        return 1
    fi
    
    print_info "显示 $service 服务的实时日志..."
    print_warning "按 Ctrl+C 退出日志查看"
    docker-compose logs -f "$service"
}

# 显示错误日志
show_error_logs() {
    print_info "显示错误日志..."
    docker-compose logs | grep -i error || echo "没有发现错误日志"
}

# 显示最近的日志
show_recent_logs() {
    local lines=${1:-100}
    print_info "显示最近 $lines 行日志..."
    docker-compose logs --tail="$lines"
}

# 重启服务
restart_service() {
    local service=$1
    
    if [ -z "$service" ]; then
        print_error "请指定服务名称"
        echo "可用服务："
        docker-compose config --services
        return 1
    fi
    
    print_info "重启 $service 服务..."
    docker-compose restart "$service"
    print_success "$service 服务已重启"
}

# 重启所有服务
restart_all() {
    print_warning "即将重启所有服务"
    read -p "继续? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "重启所有服务..."
        docker-compose restart
        print_success "所有服务已重启"
    else
        print_info "操作已取消"
    fi
}

# 清理系统
cleanup() {
    print_warning "即将清理未使用的 Docker 资源"
    read -p "继续? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "清理未使用的镜像..."
        docker image prune -f
        
        print_info "清理未使用的容器..."
        docker container prune -f
        
        print_info "清理未使用的网络..."
        docker network prune -f
        
        print_success "清理完成"
    else
        print_info "操作已取消"
    fi
}

# 启动管理工具
start_tools() {
    print_info "启动管理工具..."
    docker-compose --profile tools up -d
    
    echo ""
    print_success "管理工具已启动！"
    echo ""
    echo "可用工具："
    echo "- Portainer: http://localhost:9000 (Docker 容器管理)"
    echo "- PgAdmin: http://localhost:8080 (数据库管理)"
    echo "- Redis Commander: http://localhost:8081 (Redis 管理)"
}

# 健康检查
health_check() {
    print_info "执行健康检查..."

    # 检查 API 健康状态（尝试多个端口）
    if curl -f -s http://localhost:5001/weatherforecast > /dev/null 2>&1; then
        print_success "API 服务健康 (端口 5001)"
    elif curl -f -s http://localhost:8080/weatherforecast > /dev/null 2>&1; then
        print_success "API 服务健康 (通过容器Nginx 端口 8080)"
    elif curl -f -s http://localhost/weatherforecast > /dev/null 2>&1; then
        print_success "API 服务健康 (通过现有Nginx 端口 80)"
    else
        print_error "API 服务不健康 (尝试了端口 5001, 8080, 80)"
    fi
    
    # 检查数据库连接
    if docker-compose exec -T postgres pg_isready -U orleans > /dev/null 2>&1; then
        print_success "PostgreSQL 连接正常"
    else
        print_error "PostgreSQL 连接失败"
    fi
    
    # 检查 Redis 连接
    if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
        print_success "Redis 连接正常"
    else
        print_error "Redis 连接失败"
    fi
}

# 显示帮助信息
show_help() {
    echo "服务器监控和日志查看脚本"
    echo ""
    echo "用法: $0 <command> [arguments]"
    echo ""
    echo "监控命令:"
    echo "  status                   显示服务状态和资源使用"
    echo "  health                   执行健康检查"
    echo "  tools                    启动管理工具 (Portainer, PgAdmin, Redis Commander)"
    echo ""
    echo "日志命令:"
    echo "  logs                     显示所有服务的实时日志"
    echo "  logs <service>           显示特定服务的实时日志"
    echo "  errors                   显示错误日志"
    echo "  recent [lines]           显示最近的日志 (默认100行)"
    echo ""
    echo "管理命令:"
    echo "  restart <service>        重启特定服务"
    echo "  restart-all              重启所有服务"
    echo "  cleanup                  清理未使用的 Docker 资源"
    echo ""
    echo "示例:"
    echo "  $0 status"
    echo "  $0 logs api"
    echo "  $0 restart silo"
    echo "  $0 recent 50"
}

# 主函数
main() {
    check_directory
    
    case "${1:-}" in
        "status")
            show_status
            ;;
        "logs")
            if [ -n "$2" ]; then
                show_service_logs "$2"
            else
                show_all_logs
            fi
            ;;
        "errors")
            show_error_logs
            ;;
        "recent")
            show_recent_logs "$2"
            ;;
        "restart")
            if [ -n "$2" ]; then
                restart_service "$2"
            else
                print_error "请指定要重启的服务名称"
                echo "用法: $0 restart <service>"
            fi
            ;;
        "restart-all")
            restart_all
            ;;
        "cleanup")
            cleanup
            ;;
        "health")
            health_check
            ;;
        "tools")
            start_tools
            ;;
        "help"|"-h"|"--help"|"")
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

main "$@"
