#!/bin/bash

# Git Flow 辅助脚本
# 简化常见的分支管理操作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查是否在 git 仓库中
check_git_repo() {
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        print_error "当前目录不是 Git 仓库"
        exit 1
    fi
}

# 检查工作区是否干净
check_clean_working_tree() {
    if ! git diff-index --quiet HEAD --; then
        print_error "工作区有未提交的更改，请先提交或暂存"
        git status --short
        exit 1
    fi
}

# 更新分支
update_branch() {
    local branch=$1
    print_info "更新分支 $branch..."
    git checkout $branch
    git pull origin $branch
}

# 创建功能分支
create_feature() {
    local feature_name=$1
    
    if [ -z "$feature_name" ]; then
        print_error "请提供功能分支名称"
        echo "用法: $0 feature <feature-name>"
        exit 1
    fi
    
    local branch_name="feature/$feature_name"
    
    print_info "创建功能分支: $branch_name"
    
    # 更新 develop 分支
    update_branch "develop"
    
    # 创建功能分支
    git checkout -b $branch_name
    
    print_success "功能分支 $branch_name 创建成功"
    print_info "现在可以开始开发功能了"
    print_info "完成后使用: $0 finish-feature $feature_name"
}

# 完成功能分支
finish_feature() {
    local feature_name=$1
    
    if [ -z "$feature_name" ]; then
        print_error "请提供功能分支名称"
        echo "用法: $0 finish-feature <feature-name>"
        exit 1
    fi
    
    local branch_name="feature/$feature_name"
    local current_branch=$(git branch --show-current)
    
    if [ "$current_branch" != "$branch_name" ]; then
        print_error "当前不在功能分支 $branch_name 上"
        exit 1
    fi
    
    check_clean_working_tree
    
    print_info "完成功能分支: $branch_name"
    
    # 推送功能分支
    git push origin $branch_name
    
    print_success "功能分支已推送到远程"
    print_warning "请在 GitHub 上创建 Pull Request: $branch_name -> develop"
    print_info "PR 合并后，使用: $0 cleanup-feature $feature_name"
}

# 清理已合并的功能分支
cleanup_feature() {
    local feature_name=$1
    
    if [ -z "$feature_name" ]; then
        print_error "请提供功能分支名称"
        echo "用法: $0 cleanup-feature <feature-name>"
        exit 1
    fi
    
    local branch_name="feature/$feature_name"
    
    print_info "清理功能分支: $branch_name"
    
    # 切换到 develop
    git checkout develop
    git pull origin develop
    
    # 删除本地分支
    git branch -d $branch_name
    
    # 删除远程分支
    git push origin --delete $branch_name
    
    print_success "功能分支 $branch_name 已清理"
}

# 创建发布分支
create_release() {
    local version=$1
    
    if [ -z "$version" ]; then
        print_error "请提供版本号"
        echo "用法: $0 release <version>"
        echo "例如: $0 release v1.2.0"
        exit 1
    fi
    
    local branch_name="release/$version"
    
    print_info "创建发布分支: $branch_name"
    
    # 更新 develop 分支
    update_branch "develop"
    
    # 创建发布分支
    git checkout -b $branch_name
    
    print_success "发布分支 $branch_name 创建成功"
    print_info "请更新版本号和 CHANGELOG"
    print_info "完成后使用: $0 finish-release $version"
}

# 完成发布
finish_release() {
    local version=$1
    
    if [ -z "$version" ]; then
        print_error "请提供版本号"
        echo "用法: $0 finish-release <version>"
        exit 1
    fi
    
    local branch_name="release/$version"
    local current_branch=$(git branch --show-current)
    
    if [ "$current_branch" != "$branch_name" ]; then
        print_error "当前不在发布分支 $branch_name 上"
        exit 1
    fi
    
    check_clean_working_tree
    
    print_info "完成发布: $version"
    
    # 合并到 test 分支进行最终测试
    print_info "合并到 test 分支..."
    git checkout test
    git pull origin test
    git merge $branch_name
    git push origin test
    
    print_success "已合并到 test 分支，请等待测试完成"
    print_warning "测试通过后，使用: $0 deploy-release $version"
}

# 部署发布到生产
deploy_release() {
    local version=$1
    
    if [ -z "$version" ]; then
        print_error "请提供版本号"
        echo "用法: $0 deploy-release <version>"
        exit 1
    fi
    
    local branch_name="release/$version"
    
    print_warning "即将部署到生产环境，请确认测试已通过"
    read -p "继续部署? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "部署已取消"
        exit 0
    fi
    
    print_info "部署发布到生产: $version"
    
    # 合并到 main
    update_branch "main"
    git merge $branch_name
    git tag -a $version -m "Release $version"
    git push origin main
    git push origin $version
    
    # 合并回 develop
    update_branch "develop"
    git merge $branch_name
    git push origin develop
    
    # 删除发布分支
    git branch -d $branch_name
    git push origin --delete $branch_name
    
    print_success "发布 $version 部署成功！"
}

# 创建热修复分支
create_hotfix() {
    local hotfix_name=$1
    
    if [ -z "$hotfix_name" ]; then
        print_error "请提供热修复分支名称"
        echo "用法: $0 hotfix <hotfix-name>"
        exit 1
    fi
    
    local branch_name="hotfix/$hotfix_name"
    
    print_info "创建热修复分支: $branch_name"
    
    # 从 main 创建热修复分支
    update_branch "main"
    git checkout -b $branch_name
    
    print_success "热修复分支 $branch_name 创建成功"
    print_warning "这是紧急修复，请尽快完成"
    print_info "完成后使用: $0 finish-hotfix $hotfix_name"
}

# 完成热修复
finish_hotfix() {
    local hotfix_name=$1
    
    if [ -z "$hotfix_name" ]; then
        print_error "请提供热修复分支名称"
        echo "用法: $0 finish-hotfix <hotfix-name>"
        exit 1
    fi
    
    local branch_name="hotfix/$hotfix_name"
    local current_branch=$(git branch --show-current)
    
    if [ "$current_branch" != "$branch_name" ]; then
        print_error "当前不在热修复分支 $branch_name 上"
        exit 1
    fi
    
    check_clean_working_tree
    
    print_warning "即将部署热修复到生产环境"
    read -p "继续部署? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "部署已取消"
        exit 0
    fi
    
    print_info "完成热修复: $hotfix_name"
    
    # 合并到 main
    update_branch "main"
    git merge $branch_name
    git push origin main
    
    # 合并到 develop
    update_branch "develop"
    git merge $branch_name
    git push origin develop
    
    # 删除热修复分支
    git branch -d $branch_name
    git push origin --delete $branch_name
    
    print_success "热修复 $hotfix_name 部署成功！"
}

# 显示帮助信息
show_help() {
    echo "Git Flow 辅助脚本"
    echo ""
    echo "用法: $0 <command> [arguments]"
    echo ""
    echo "功能分支:"
    echo "  feature <name>           创建功能分支"
    echo "  finish-feature <name>    完成功能分支（推送并提示创建PR）"
    echo "  cleanup-feature <name>   清理已合并的功能分支"
    echo ""
    echo "发布分支:"
    echo "  release <version>        创建发布分支"
    echo "  finish-release <version> 完成发布（合并到test）"
    echo "  deploy-release <version> 部署发布到生产"
    echo ""
    echo "热修复分支:"
    echo "  hotfix <name>            创建热修复分支"
    echo "  finish-hotfix <name>     完成热修复（直接部署到生产）"
    echo ""
    echo "示例:"
    echo "  $0 feature user-auth"
    echo "  $0 release v1.2.0"
    echo "  $0 hotfix critical-bug"
}

# 主函数
main() {
    check_git_repo
    
    case "${1:-}" in
        "feature")
            create_feature "$2"
            ;;
        "finish-feature")
            finish_feature "$2"
            ;;
        "cleanup-feature")
            cleanup_feature "$2"
            ;;
        "release")
            create_release "$2"
            ;;
        "finish-release")
            finish_release "$2"
            ;;
        "deploy-release")
            deploy_release "$2"
            ;;
        "hotfix")
            create_hotfix "$2"
            ;;
        "finish-hotfix")
            finish_hotfix "$2"
            ;;
        "help"|"-h"|"--help"|"")
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

main "$@"
