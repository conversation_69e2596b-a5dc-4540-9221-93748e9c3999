#!/bin/bash

# 基础设施组件管理脚本
# 用途：统一管理不同环境下的基础设施组件（PostgreSQL、Redis、监控等）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 获取脚本所在目录的父目录（项目根目录）
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 环境配置
LOCAL_INFRA_DIR="$PROJECT_ROOT/infrastructure/local"
DEV_INFRA_DIR="$PROJECT_ROOT/infrastructure/development"
PROD_INFRA_DIR="$PROJECT_ROOT/infrastructure/production"

# 检查 Docker 是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker 未运行，请先启动 Docker"
        exit 1
    fi
}

# 检查目录是否存在
check_directory() {
    local dir=$1
    if [ ! -d "$dir" ]; then
        print_error "目录不存在: $dir"
        exit 1
    fi
}

# 启动本地开发环境
start_local() {
    local include_monitoring=${1:-false}
    local include_tools=${2:-false}
    
    print_info "启动本地开发环境基础设施..."
    check_directory "$LOCAL_INFRA_DIR"
    
    cd "$LOCAL_INFRA_DIR"
    
    # 启动基础服务
    print_info "启动基础服务 (PostgreSQL, Redis)..."
    if [ "$include_tools" = "true" ]; then
        docker-compose --profile tools up -d
    else
        docker-compose up -d
    fi
    
    # 启动监控服务
    if [ "$include_monitoring" = "true" ]; then
        print_info "启动监控服务..."
        docker-compose -f docker-compose.monitoring.yml up -d
    fi
    
    # 等待服务就绪
    print_info "等待服务就绪..."
    sleep 10
    
    show_local_status
}

# 停止本地开发环境
stop_local() {
    print_info "停止本地开发环境..."
    check_directory "$LOCAL_INFRA_DIR"
    
    cd "$LOCAL_INFRA_DIR"
    
    # 停止监控服务
    if [ -f "docker-compose.monitoring.yml" ]; then
        docker-compose -f docker-compose.monitoring.yml down
    fi
    
    # 停止基础服务
    docker-compose --profile tools down
    
    print_success "本地开发环境已停止"
}

# 显示本地环境状态
show_local_status() {
    print_success "本地开发环境启动完成！"
    echo ""
    print_info "基础服务："
    echo "  🐘 PostgreSQL:       localhost:5432 (orleans/**********)"
    echo "  🔴 Redis:            localhost:6379"
    echo ""
    
    # 检查是否启动了管理工具
    if docker ps --format "table {{.Names}}" | grep -q "pgadmin"; then
        print_info "管理工具："
        echo "  📊 PgAdmin:          http://localhost:8080 (<EMAIL>/admin123)"
        echo "  🔧 Redis Commander: http://localhost:8081"
        echo ""
    fi
    
    # 检查是否启动了监控服务
    if docker ps --format "table {{.Names}}" | grep -q "seq"; then
        print_info "监控服务："
        echo "  📊 Seq 日志:         http://localhost:5341"
        echo "  🔍 Jaeger 追踪:      http://localhost:16686"
        echo "  📈 Grafana 监控:     http://localhost:3000 (admin/admin)"
        echo "  📊 Prometheus:       http://localhost:9090"
        echo ""
    fi
    
    print_info "应用程序连接配置："
    echo "  ConnectionStrings:redis=localhost:6379"
    echo "  ConnectionStrings:orleansdb=Host=localhost;Database=orleansdb;Username=orleans;Password=**********"
    echo ""
    print_warning "现在可以启动你的应用程序："
    echo "  dotnet run --project src/Curios.Silo"
    echo "  dotnet run --project src/Curios.Api"
}

# 启动开发环境
start_development() {
    print_info "启动开发环境..."
    check_directory "$PROJECT_ROOT/deploy/docker"
    
    cd "$PROJECT_ROOT/deploy/docker"
    docker-compose -f docker-compose.dev.yml up -d
    
    print_success "开发环境启动完成！"
    print_info "访问地址："
    echo "  🌐 API:              http://localhost"
    echo "  📊 Portainer:        http://localhost:9000"
}

# 启动生产环境
start_production() {
    print_info "启动生产环境..."
    check_directory "$PROJECT_ROOT/deploy/docker"
    
    cd "$PROJECT_ROOT/deploy/docker"
    docker-compose -f docker-compose.yml up -d
    
    print_success "生产环境启动完成！"
    print_info "访问地址："
    echo "  🌐 API:              http://localhost"
    echo "  📊 Portainer:        http://localhost:9000"
}

# 显示服务状态
show_status() {
    local env=${1:-local}
    
    case $env in
        "local")
            if [ -d "$LOCAL_INFRA_DIR" ]; then
                cd "$LOCAL_INFRA_DIR"
                print_info "本地环境服务状态："
                docker-compose ps
                if [ -f "docker-compose.monitoring.yml" ]; then
                    echo ""
                    print_info "监控服务状态："
                    docker-compose -f docker-compose.monitoring.yml ps
                fi
            fi
            ;;
        "dev"|"development")
            cd "$PROJECT_ROOT/deploy/docker"
            print_info "开发环境服务状态："
            docker-compose -f docker-compose.dev.yml ps
            ;;
        "prod"|"production")
            cd "$PROJECT_ROOT/deploy/docker"
            print_info "生产环境服务状态："
            docker-compose -f docker-compose.yml ps
            ;;
        *)
            print_error "未知环境: $env"
            exit 1
            ;;
    esac
}

# 显示日志
show_logs() {
    local env=${1:-local}
    local service=${2:-""}
    
    case $env in
        "local")
            cd "$LOCAL_INFRA_DIR"
            if [ -n "$service" ]; then
                docker-compose logs -f "$service"
            else
                docker-compose logs -f
            fi
            ;;
        "dev"|"development")
            cd "$PROJECT_ROOT/deploy/docker"
            if [ -n "$service" ]; then
                docker-compose -f docker-compose.dev.yml logs -f "$service"
            else
                docker-compose -f docker-compose.dev.yml logs -f
            fi
            ;;
        "prod"|"production")
            cd "$PROJECT_ROOT/deploy/docker"
            if [ -n "$service" ]; then
                docker-compose -f docker-compose.yml logs -f "$service"
            else
                docker-compose -f docker-compose.yml logs -f
            fi
            ;;
        *)
            print_error "未知环境: $env"
            exit 1
            ;;
    esac
}

# 清理资源
cleanup() {
    print_info "清理 Docker 资源..."
    docker system prune -f
    docker volume prune -f
    print_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "基础设施组件管理脚本"
    echo ""
    echo "用法: $0 <command> [options]"
    echo ""
    echo "环境管理命令:"
    echo "  local [monitoring] [tools]  启动本地开发环境"
    echo "    monitoring                 同时启动监控服务"
    echo "    tools                      同时启动管理工具"
    echo "  development                  启动开发环境"
    echo "  production                   启动生产环境"
    echo "  stop <env>                   停止指定环境"
    echo ""
    echo "监控命令:"
    echo "  status [env]                 显示服务状态"
    echo "  logs <env> [service]         显示日志"
    echo ""
    echo "维护命令:"
    echo "  cleanup                      清理 Docker 资源"
    echo ""
    echo "示例:"
    echo "  $0 local                     # 启动本地基础服务"
    echo "  $0 local monitoring          # 启动本地基础服务 + 监控"
    echo "  $0 local monitoring tools    # 启动本地基础服务 + 监控 + 管理工具"
    echo "  $0 development               # 启动开发环境"
    echo "  $0 status local              # 查看本地环境状态"
    echo "  $0 logs local postgres       # 查看本地 PostgreSQL 日志"
    echo "  $0 stop local                # 停止本地环境"
}

# 主函数
main() {
    check_docker
    
    case "${1:-help}" in
        "local")
            local include_monitoring=false
            local include_tools=false
            
            # 检查参数
            for arg in "${@:2}"; do
                case $arg in
                    "monitoring") include_monitoring=true ;;
                    "tools") include_tools=true ;;
                esac
            done
            
            start_local "$include_monitoring" "$include_tools"
            ;;
        "development"|"dev")
            start_development
            ;;
        "production"|"prod")
            start_production
            ;;
        "stop")
            case "${2:-local}" in
                "local") stop_local ;;
                "dev"|"development") 
                    cd "$PROJECT_ROOT/deploy/docker"
                    docker-compose -f docker-compose.dev.yml down
                    ;;
                "prod"|"production")
                    cd "$PROJECT_ROOT/deploy/docker"
                    docker-compose -f docker-compose.yml down
                    ;;
                *) print_error "未知环境: $2" ;;
            esac
            ;;
        "status")
            show_status "${2:-local}"
            ;;
        "logs")
            show_logs "${2:-local}" "$3"
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            echo "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
