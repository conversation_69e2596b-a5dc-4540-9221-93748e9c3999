version: '3.8'

# Production overrides for docker-compose.yml
services:
  redis:
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

  postgres:
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    environment:
      POSTGRES_DB: orleansdb
      POSTGRES_USER: orleans
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-orleans123}
    command: postgres -c max_connections=200 -c shared_buffers=256MB

  silo1:
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__redis=redis:6379
      - ConnectionStrings__orleansdb=Host=postgres;Database=orleansdb;Username=orleans;Password=${POSTGRES_PASSWORD:-orleans123}
      - ORLEANS_SILO_NAME=silo1
      - ORLEANS_CLUSTER_ID=curios-cluster
      - ORLEANS_SERVICE_ID=curios-service
      - ASPNETCORE_URLS=http://+:8080

  silo2:
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__redis=redis:6379
      - ConnectionStrings__orleansdb=Host=postgres;Database=orleansdb;Username=orleans;Password=${POSTGRES_PASSWORD:-orleans123}
      - ORLEANS_SILO_NAME=silo2
      - ORLEANS_CLUSTER_ID=curios-cluster
      - ORLEANS_SERVICE_ID=curios-service
      - ASPNETCORE_URLS=http://+:8080

  api1:
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__redis=redis:6379
      - ORLEANS_CLUSTER_ID=curios-cluster
      - ORLEANS_SERVICE_ID=curios-service
      - ASPNETCORE_URLS=http://+:8080

  api2:
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__redis=redis:6379
      - ORLEANS_CLUSTER_ID=curios-cluster
      - ORLEANS_SERVICE_ID=curios-service
      - ASPNETCORE_URLS=http://+:8080

  nginx:
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M
