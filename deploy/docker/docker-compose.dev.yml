version: '3.8'

services:
  # Development Infrastructure Services
  redis:
    image: redis:7-alpine
    container_name: curios-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  postgres:
    image: postgres:16-alpine
    container_name: curios-postgres-dev
    environment:
      POSTGRES_DB: orleansdb
      POSTGRES_USER: orleans
      POSTGRES_PASSWORD: orleans123
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./init-orleans-db.sql:/docker-entrypoint-initdb.d/init-orleans-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U orleans -d orleansdb"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Development Orleans Silo
  silo:
    image: ghcr.io/hxw/curios-api-silo:develop
    container_name: curios-silo-dev
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__redis=redis:6379
      - ConnectionStrings__orleansdb=Host=postgres;Database=orleansdb;Username=orleans;Password=orleans123
      - ORLEANS_SILO_NAME=silo-dev
      - ORLEANS_CLUSTER_ID=curios-dev-cluster
      - ORLEANS_SERVICE_ID=curios-dev-service
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
    ports:
      - "11111:8080"

  # Development API
  api:
    image: ghcr.io/hxw/curios-api-api:develop
    container_name: curios-api-dev
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__redis=redis:6379
      - ORLEANS_CLUSTER_ID=curios-dev-cluster
      - ORLEANS_SERVICE_ID=curios-dev-service
    depends_on:
      - silo
    ports:
      - "5001:8080"  # 直接暴露API端口，供现有Nginx代理

  # Development Load Balancer (可选 - 如果服务器已有Nginx则注释掉)
  nginx:
    image: nginx:alpine
    container_name: curios-nginx-dev
    ports:
      - "8080:80"  # 使用8080端口避免与现有Nginx冲突
    volumes:
      - ./nginx.dev.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - api
    profiles:
      - nginx  # 使用profile控制是否启动

  # Development Management Tools (Optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: curios-pgadmin-dev
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "8080:80"
    depends_on:
      - postgres
    profiles:
      - tools

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: curios-redis-commander-dev
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    profiles:
      - tools

  # Container Management UI
  portainer:
    image: portainer/portainer-ce:latest
    container_name: curios-portainer-dev
    ports:
      - "9000:9000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_dev_data:/data
    restart: unless-stopped
    profiles:
      - tools

volumes:
  redis_dev_data:
  postgres_dev_data:
  portainer_dev_data:
