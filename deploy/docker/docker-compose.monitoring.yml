version: '3.8'

services:
  # 日志管理 - Seq
  seq:
    image: datalust/seq:latest
    container_name: curios-seq
    environment:
      - ACCEPT_EULA=Y
    ports:
      - "5341:80"
    volumes:
      - seq_data:/data
    restart: unless-stopped

  # 分布式追踪 - Jaeger
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: curios-jaeger
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # HTTP collector
      - "14250:14250"  # gRPC collector
    environment:
      - COLLECTOR_OTLP_ENABLED=true
      - SPAN_STORAGE_TYPE=memory
    restart: unless-stopped

  # 指标收集 - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: curios-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  # 监控面板 - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: curios-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
    restart: unless-stopped
    depends_on:
      - prometheus

  # Redis 监控
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: curios-redis-exporter
    ports:
      - "9121:9121"
    environment:
      - REDIS_ADDR=redis://redis:6379
    depends_on:
      - redis
    restart: unless-stopped

  # PostgreSQL 监控
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    container_name: curios-postgres-exporter
    ports:
      - "9187:9187"
    environment:
      - DATA_SOURCE_NAME=*********************************************/orleansdb?sslmode=disable
    depends_on:
      - postgres
    restart: unless-stopped

volumes:
  seq_data:
  prometheus_data:
  grafana_data:

networks:
  default:
    external: true
    name: curios-api-dev_default
