events {
    worker_connections 1024;
}

http {
    # Production API Backend
    upstream api_backend {
        server api1:8080;
        server api2:8080;
    }

    # Production Environment
    server {
        listen 80;
        server_name localhost yourdomain.com api.yourdomain.com;
        
        location / {
            proxy_pass http://api_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /health {
            proxy_pass http://api_backend/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Production-specific headers
        add_header X-Environment "Production" always;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    }

    # Default server (fallback)
    server {
        listen 80 default_server;
        server_name _;
        return 444; # Close connection without response
    }
}
