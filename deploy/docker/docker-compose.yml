version: '3.8'

services:
  # Production Infrastructure Services
  redis:
    image: redis:7-alpine
    container_name: curios-redis-prod
    ports:
      - "6379:6379"
    volumes:
      - redis_prod_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru

  postgres:
    image: postgres:16-alpine
    container_name: curios-postgres-prod
    environment:
      POSTGRES_DB: orleansdb
      POSTGRES_USER: orleans
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-orleans123}
    ports:
      - "5432:5432"
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./init-orleans-db.sql:/docker-entrypoint-initdb.d/init-orleans-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U orleans -d orleansdb"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: postgres -c max_connections=200 -c shared_buffers=256MB

  # Production Orleans Silo Services
  silo1:
    image: ghcr.io/hxw/curios-api-silo:latest
    container_name: curios-silo-prod1
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__redis=redis:6379
      - ConnectionStrings__orleansdb=Host=postgres;Database=orleansdb;Username=orleans;Password=${POSTGRES_PASSWORD:-orleans123}
      - ORLEANS_SILO_NAME=silo-prod1
      - ORLEANS_CLUSTER_ID=curios-prod-cluster
      - ORLEANS_SERVICE_ID=curios-prod-service
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
    ports:
      - "11111:8080"
      - "30000:30000"

  silo2:
    image: ghcr.io/hxw/curios-api-silo:latest
    container_name: curios-silo-prod2
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__redis=redis:6379
      - ConnectionStrings__orleansdb=Host=postgres;Database=orleansdb;Username=orleans;Password=${POSTGRES_PASSWORD:-orleans123}
      - ORLEANS_SILO_NAME=silo-prod2
      - ORLEANS_CLUSTER_ID=curios-prod-cluster
      - ORLEANS_SERVICE_ID=curios-prod-service
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
      silo1:
        condition: service_started
    ports:
      - "11112:8080"
      - "30001:30000"

  # Production API Services
  api1:
    image: ghcr.io/hxw/curios-api-api:latest
    container_name: curios-api-prod1
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__redis=redis:6379
      - ORLEANS_CLUSTER_ID=curios-prod-cluster
      - ORLEANS_SERVICE_ID=curios-prod-service
    depends_on:
      - silo1
      - silo2
    ports:
      - "5001:8080"

  api2:
    image: ghcr.io/hxw/curios-api-api:latest
    container_name: curios-api-prod2
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__redis=redis:6379
      - ORLEANS_CLUSTER_ID=curios-prod-cluster
      - ORLEANS_SERVICE_ID=curios-prod-service
    depends_on:
      - silo1
      - silo2
    ports:
      - "5002:8080"

  # Production Load Balancer
  nginx:
    image: nginx:alpine
    container_name: curios-nginx-prod
    ports:
      - "80:80"
    volumes:
      - ./nginx.prod.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - api1
      - api2

  # Production Management Tools (Optional)
  portainer:
    image: portainer/portainer-ce:latest
    container_name: curios-portainer-prod
    ports:
      - "9000:9000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_prod_data:/data
    restart: unless-stopped
    profiles:
      - tools

volumes:
  redis_prod_data:
  postgres_prod_data:
  portainer_prod_data:
