events {
    worker_connections 1024;
}

http {
    # Development API Backend
    upstream dev_api_backend {
        server api-dev:8080;
    }

    # Production API Backend
    upstream prod_api_backend {
        server api-prod1:8080;
        server api-prod2:8080;
    }

    # Development Environment
    server {
        listen 80;
        server_name dev.yourdomain.com localhost;

        location / {
            proxy_pass http://dev_api_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /health {
            proxy_pass http://dev_api_backend/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Add development-specific headers
        add_header X-Environment "Development" always;
    }

    # Production Environment
    server {
        listen 80;
        server_name yourdomain.com api.yourdomain.com;

        location / {
            proxy_pass http://prod_api_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /health {
            proxy_pass http://prod_api_backend/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Add production-specific headers
        add_header X-Environment "Production" always;

        # Production security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }

    # Default server (fallback)
    server {
        listen 80 default_server;
        server_name _;
        return 444; # Close connection without response
    }
}
