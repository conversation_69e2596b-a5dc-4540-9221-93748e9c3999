global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus 自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # .NET 应用监控 (需要添加 prometheus-net 包)
  - job_name: 'curios-api'
    static_configs:
      - targets: ['api1:8080', 'api2:8080']
    metrics_path: '/metrics'
    scrape_interval: 5s

  - job_name: 'curios-silo'
    static_configs:
      - targets: ['silo1:8080', 'silo2:8080']
    metrics_path: '/metrics'
    scrape_interval: 5s

  # Redis 监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  # PostgreSQL 监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Nginx 监控 (需要启用 stub_status)
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    metrics_path: '/nginx_status'
