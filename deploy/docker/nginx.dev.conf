events {
    worker_connections 1024;
}

http {
    # Development API Backend
    upstream api_backend {
        server api:8080;
    }

    # Development Environment
    server {
        listen 80;
        server_name localhost dev.yourdomain.com;
        
        location / {
            proxy_pass http://api_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /health {
            proxy_pass http://api_backend/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Add development-specific headers
        add_header X-Environment "Development" always;
        add_header X-Debug "Enabled" always;
    }
}
