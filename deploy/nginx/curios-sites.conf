# Curios API Nginx 配置示例
# 将此文件复制到你的服务器 /etc/nginx/sites-available/ 目录

# 开发环境配置
server {
    listen 80;
    server_name dev.yourdomain.com;
    
    # API 反向代理
    location / {
        proxy_pass http://localhost:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 健康检查端点
    location /health {
        proxy_pass http://localhost:5001/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 健康检查不需要长时间等待
        proxy_connect_timeout 5s;
        proxy_send_timeout 5s;
        proxy_read_timeout 5s;
    }
    
    # Orleans Silo 管理界面（可选）
    location /silo/ {
        proxy_pass http://localhost:11111/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Portainer 管理界面（可选）
    location /portainer/ {
        proxy_pass http://localhost:9000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Portainer 需要 WebSocket
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 开发环境标识
    add_header X-Environment "Development" always;
    
    # 开发环境允许跨域（谨慎使用）
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
    
    # 处理 OPTIONS 请求
    if ($request_method = 'OPTIONS') {
        return 204;
    }
}

# 生产环境配置
server {
    listen 80;
    server_name yourdomain.com api.yourdomain.com;
    
    # 负载均衡到多个API实例
    location / {
        proxy_pass http://curios_api_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 生产环境超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # 健康检查端点
    location /health {
        proxy_pass http://curios_api_backend/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 快速健康检查
        proxy_connect_timeout 5s;
        proxy_send_timeout 5s;
        proxy_read_timeout 5s;
    }
    
    # 限制管理端点访问（生产环境安全）
    location ~ ^/(silo|portainer)/ {
        # 只允许内网访问
        allow 10.0.0.0/8;
        allow **********/12;
        allow ***********/16;
        deny all;
        
        # 根据路径代理到不同服务
        if ($uri ~ ^/silo/(.*)$) {
            proxy_pass http://localhost:11111/$1;
        }
        if ($uri ~ ^/portainer/(.*)$) {
            proxy_pass http://localhost:9000/$1;
        }
        
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 生产环境安全头
    add_header X-Environment "Production" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 禁用服务器版本信息
    server_tokens off;
}

# 负载均衡配置
upstream curios_api_backend {
    # 生产环境的多个API实例
    server localhost:5002 weight=1 max_fails=3 fail_timeout=30s;
    server localhost:5003 weight=1 max_fails=3 fail_timeout=30s;
    
    # 负载均衡策略
    # least_conn;  # 最少连接
    # ip_hash;     # IP哈希（会话保持）
}

# SSL 配置示例（推荐生产环境使用）
# server {
#     listen 443 ssl http2;
#     server_name yourdomain.com api.yourdomain.com;
#     
#     ssl_certificate /path/to/your/certificate.crt;
#     ssl_certificate_key /path/to/your/private.key;
#     
#     # SSL 安全配置
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     
#     # HSTS
#     add_header Strict-Transport-Security "max-age=63072000" always;
#     
#     # 其他配置与HTTP版本相同...
# }

# HTTP 重定向到 HTTPS（如果使用SSL）
# server {
#     listen 80;
#     server_name yourdomain.com api.yourdomain.com;
#     return 301 https://$server_name$request_uri;
# }
