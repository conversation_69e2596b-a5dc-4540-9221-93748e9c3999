#!/bin/bash

# Start development environment
echo "Starting development environment..."

cd "$(dirname "$0")/../docker"

# 检查是否需要启动容器内的Nginx
if [ "$1" = "--with-nginx" ]; then
    echo "启动包含Nginx容器的完整环境..."
    docker-compose -f docker-compose.dev.yml --profile nginx up -d
    NGINX_INFO="- API (通过容器Nginx): http://localhost:8080"
else
    echo "启动无Nginx容器的环境（适合现有Nginx服务器）..."
    docker-compose -f docker-compose.dev.yml up -d
    NGINX_INFO="- API (直接访问): http://localhost:5001"
fi

echo "Development environment started!"
echo ""
echo "Services available at:"
echo "$NGINX_INFO"
echo "- Silo Dashboard: http://localhost:11111"
echo "- Redis: localhost:6379"
echo "- PostgreSQL: localhost:5432 (user: orleans, password: orleans123, db: orleansdb)"
echo ""
echo "Management Tools (optional):"
echo "- 启动管理工具: docker-compose -f docker-compose.dev.yml --profile tools up -d"
echo "- Portainer: http://localhost:9000 (Docker管理)"
echo "- PgAdmin: http://localhost:8080 (数据库管理)"
echo "- Redis Commander: http://localhost:8081 (Redis管理)"
echo ""
if [ "$1" != "--with-nginx" ]; then
    echo "🌐 Nginx 整合提示:"
    echo "- 如果你的服务器已有Nginx，请配置反向代理到 localhost:5001"
    echo "- 参考配置文件: deploy/nginx/curios-sites.conf"
    echo "- 或者使用 --with-nginx 参数启动容器内的Nginx"
    echo ""
fi
echo "To stop all services: docker-compose -f docker-compose.dev.yml down"
echo "To view logs: docker-compose -f docker-compose.dev.yml logs -f"
