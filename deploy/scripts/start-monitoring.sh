#!/bin/bash

# 启动监控服务脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查 Docker 是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker 未运行，请先启动 Docker"
        exit 1
    fi
}

# 检查是否在正确的目录
check_directory() {
    if [ ! -f "docker-compose.monitoring.yml" ]; then
        print_error "请在 deploy/docker 目录中运行此脚本"
        exit 1
    fi
}

# 创建必要的目录
create_directories() {
    print_info "创建必要的目录..."
    mkdir -p grafana/provisioning/dashboards
    mkdir -p grafana/provisioning/datasources
    mkdir -p logs
}

# 创建 Grafana 数据源配置
create_grafana_datasources() {
    print_info "创建 Grafana 数据源配置..."
    
    cat > grafana/provisioning/datasources/datasources.yml << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true

  - name: Jaeger
    type: jaeger
    access: proxy
    url: http://jaeger:16686
    editable: true
EOF
}

# 启动监控服务
start_monitoring() {
    print_info "启动监控服务..."
    
    # 首先启动基础设施
    print_info "启动基础设施服务 (Redis, PostgreSQL)..."
    docker-compose -f docker-compose.dev.yml up -d redis postgres
    
    # 等待基础设施就绪
    print_info "等待基础设施服务就绪..."
    sleep 10
    
    # 启动监控服务
    print_info "启动监控服务..."
    docker-compose -f docker-compose.monitoring.yml up -d
    
    # 等待监控服务启动
    print_info "等待监控服务启动..."
    sleep 15
}

# 显示访问信息
show_access_info() {
    print_success "监控服务启动完成！"
    echo ""
    print_info "访问地址："
    echo "  📊 Seq 日志管理:      http://localhost:5341"
    echo "  🔍 Jaeger 分布式追踪: http://localhost:16686"
    echo "  📈 Grafana 监控面板:  http://localhost:3000 (admin/admin)"
    echo "  📊 Prometheus 指标:   http://localhost:9090"
    echo ""
    print_info "Redis 和 PostgreSQL 也已启动："
    echo "  🔴 Redis:            localhost:6379"
    echo "  🐘 PostgreSQL:       localhost:5432 (orleans/orleans123)"
    echo ""
    print_warning "现在可以启动你的应用程序："
    echo "  dotnet run --project ../../src/Curios.Silo"
    echo "  dotnet run --project ../../src/Curios.Api"
}

# 检查服务状态
check_services() {
    print_info "检查服务状态..."
    docker-compose -f docker-compose.monitoring.yml ps
    docker-compose -f docker-compose.dev.yml ps redis postgres
}

# 主函数
main() {
    print_info "启动监控环境..."
    
    check_docker
    check_directory
    create_directories
    create_grafana_datasources
    start_monitoring
    check_services
    show_access_info
}

# 处理命令行参数
case "${1:-start}" in
    "start")
        main
        ;;
    "stop")
        print_info "停止监控服务..."
        docker-compose -f docker-compose.monitoring.yml down
        docker-compose -f docker-compose.dev.yml stop redis postgres
        print_success "监控服务已停止"
        ;;
    "restart")
        print_info "重启监控服务..."
        docker-compose -f docker-compose.monitoring.yml restart
        print_success "监控服务已重启"
        ;;
    "status")
        check_services
        ;;
    "logs")
        service=${2:-""}
        if [ -n "$service" ]; then
            docker-compose -f docker-compose.monitoring.yml logs -f "$service"
        else
            docker-compose -f docker-compose.monitoring.yml logs -f
        fi
        ;;
    "help"|"-h"|"--help")
        echo "监控服务管理脚本"
        echo ""
        echo "用法: $0 [command]"
        echo ""
        echo "命令:"
        echo "  start    启动监控服务 (默认)"
        echo "  stop     停止监控服务"
        echo "  restart  重启监控服务"
        echo "  status   查看服务状态"
        echo "  logs     查看日志 (可指定服务名)"
        echo "  help     显示帮助信息"
        echo ""
        echo "示例:"
        echo "  $0 start"
        echo "  $0 logs seq"
        echo "  $0 logs jaeger"
        ;;
    *)
        print_error "未知命令: $1"
        echo "使用 '$0 help' 查看帮助信息"
        exit 1
        ;;
esac
