#!/bin/bash

# Start production environment with distributed Orleans setup
echo "Starting production environment..."

cd "$(dirname "$0")/../docker"

# Start all production services
docker-compose -f docker-compose.yml up -d

echo "Production environment started!"
echo ""
echo "Services available at:"
echo "- Load Balanced API: http://localhost"
echo "- API Instance 1: http://localhost:5001"
echo "- API Instance 2: http://localhost:5002"
echo "- Silo Instance 1: http://localhost:11111"
echo "- Silo Instance 2: http://localhost:11112"
echo "- Redis: localhost:6379"
echo "- PostgreSQL: localhost:5432"
echo ""
echo "To stop all services: docker-compose -f docker-compose.yml down"
echo "To view logs: docker-compose -f docker-compose.yml logs -f"
echo "To scale services: docker-compose -f docker-compose.yml up --scale api1=2 --scale api2=2"
