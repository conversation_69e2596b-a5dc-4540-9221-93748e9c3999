#!/bin/bash

# Deploy to Kubernetes
echo "Deploying Curios API to Kubernetes..."

NAMESPACE="curios-api"
SCRIPT_DIR="$(dirname "$0")"
K8S_DIR="$SCRIPT_DIR/../k8s"

# Create namespace
echo "Creating namespace..."
kubectl apply -f "$K8S_DIR/namespace.yaml"

# Deploy Redis
echo "Deploying Redis..."
kubectl apply -f "$K8S_DIR/redis.yaml"

# Wait for Redis to be ready
echo "Waiting for Redis to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/redis -n $NAMESPACE

echo "Deployment completed!"
echo ""
echo "To check status:"
echo "kubectl get pods -n $NAMESPACE"
echo ""
echo "To view logs:"
echo "kubectl logs -f deployment/redis -n $NAMESPACE"
echo ""
echo "To delete deployment:"
echo "kubectl delete namespace $NAMESPACE"
