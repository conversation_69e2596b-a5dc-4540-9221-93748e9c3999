# 开发环境配置
# 用途：开发环境部署时的环境变量配置
# 使用：在 Docker Compose 中加载此配置

# 基础设施连接信息
REDIS_CONNECTION_STRING=redis:6379
POSTGRES_CONNECTION_STRING=Host=postgres;Database=orleansdb;Username=orleans;Password=orleans123
POSTGRES_PASSWORD=orleans123

# Orleans 集群配置
ORLEANS_CLUSTER_ID=curios-dev-cluster
ORLEANS_SERVICE_ID=curios-dev-service

# 监控配置
SEQ_SERVER_URL=http://seq:80
JAEGER_ENDPOINT=http://jaeger:14268/api/traces
PROMETHEUS_ENDPOINT=http://prometheus:9090

# 应用程序配置
ASPNETCORE_ENVIRONMENT=Development

# 日志级别
LOGGING_LEVEL_DEFAULT=Information
LOGGING_LEVEL_MICROSOFT=Warning
LOGGING_LEVEL_ORLEANS=Information

# 邮件配置（开发环境）
EMAIL_SMTP_HOST=smtp.qq.com
EMAIL_SMTP_PORT=587
EMAIL_SMTP_USERNAME=<EMAIL>
EMAIL_SMTP_PASSWORD=5224
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=Curios Dev

# 安全配置
ALLOWED_HOSTS=*
