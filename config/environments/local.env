# 本地开发环境配置
# 用途：本地开发时的环境变量配置
# 使用：在启动应用程序时加载此配置

# 基础设施连接信息
REDIS_CONNECTION_STRING=localhost:6379
POSTGRES_CONNECTION_STRING=Host=localhost;Database=orleansdb;Username=orleans;Password=orleans123

# Orleans 集群配置
ORLEANS_CLUSTER_ID=curios-local-cluster
ORLEANS_SERVICE_ID=curios-local-service
ORLEANS_SILO_NAME=silo-local

# 监控配置
SEQ_SERVER_URL=http://localhost:5341
JAEGER_ENDPOINT=http://localhost:14268/api/traces
PROMETHEUS_ENDPOINT=http://localhost:9090

# 应用程序配置
ASPNETCORE_ENVIRONMENT=Development
ASPNETCORE_URLS=http://localhost:5314

# 日志级别
LOGGING_LEVEL_DEFAULT=Information
LOGGING_LEVEL_MICROSOFT=Warning
LOGGING_LEVEL_ORLEANS=Information

# 邮件配置（开发环境）
EMAIL_SMTP_HOST=smtp.qq.com
EMAIL_SMTP_PORT=587
EMAIL_SMTP_USERNAME=<EMAIL>
EMAIL_SMTP_PASSWORD=5224
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=Curios
