# 生产环境配置
# 用途：生产环境部署时的环境变量配置
# 使用：在 Docker Compose 中加载此配置
# 注意：敏感信息应通过 Docker Secrets 或外部配置管理

# 基础设施连接信息
REDIS_CONNECTION_STRING=redis:6379
POSTGRES_CONNECTION_STRING=Host=postgres;Database=orleansdb;Username=orleans;Password=${POSTGRES_PASSWORD}
POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-orleans123}

# Orleans 集群配置
ORLEANS_CLUSTER_ID=curios-prod-cluster
ORLEANS_SERVICE_ID=curios-prod-service

# 监控配置
SEQ_SERVER_URL=http://seq:80
JAEGER_ENDPOINT=http://jaeger:14268/api/traces
PROMETHEUS_ENDPOINT=http://prometheus:9090

# 应用程序配置
ASPNETCORE_ENVIRONMENT=Production

# 日志级别（生产环境更严格）
LOGGING_LEVEL_DEFAULT=Warning
LOGGING_LEVEL_MICROSOFT=Error
LOGGING_LEVEL_ORLEANS=Warning

# 邮件配置（生产环境 - 使用环境变量）
EMAIL_SMTP_HOST=${EMAIL_SMTP_HOST:-smtp.qq.com}
EMAIL_SMTP_PORT=${EMAIL_SMTP_PORT:-587}
EMAIL_SMTP_USERNAME=${EMAIL_SMTP_USERNAME}
EMAIL_SMTP_PASSWORD=${EMAIL_SMTP_PASSWORD}
EMAIL_FROM_ADDRESS=${EMAIL_FROM_ADDRESS}
EMAIL_FROM_NAME=${EMAIL_FROM_NAME:-Curios}

# 安全配置
ALLOWED_HOSTS=${ALLOWED_HOSTS:-yourdomain.com,www.yourdomain.com}

# 性能配置
ASPNETCORE_FORWARDEDHEADERS_ENABLED=true
ASPNETCORE_HOSTFILTERING_ENABLED=true
