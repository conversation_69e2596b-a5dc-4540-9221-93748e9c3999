﻿using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Orleans.TestingHost;
using Orleans.Hosting;
using System.Net.Http.Json;
using System.Text.Json;
using Curios.Grains.Interfaces;
using Curios.Grains;

namespace Curios.Api.Tests;

public class CustomWebApplicationFactory : WebApplicationFactory<Program>
{
    private TestCluster? _cluster;

    protected override IHost CreateHost(IHostBuilder builder)
    {
        // Create Orleans test cluster
        var clusterBuilder = new TestClusterBuilder();
        clusterBuilder.AddSiloBuilderConfigurator<TestSiloConfigurator>();
        _cluster = clusterBuilder.Build();
        _cluster.Deploy();

        // Configure the web host to use the test cluster
        builder.ConfigureServices(services =>
        {
            // Remove the existing Orleans client registration
            var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(Orleans.IClusterClient));
            if (descriptor != null)
            {
                services.Remove(descriptor);
            }

            // Add the test cluster client
            services.AddSingleton(_cluster.Client);
            services.AddSingleton<Orleans.IClusterClient>(_cluster.Client);
        });

        return base.CreateHost(builder);
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            _cluster?.StopAllSilos();
            _cluster?.Dispose();
        }
        base.Dispose(disposing);
    }
}

public class TestSiloConfigurator : ISiloConfigurator
{
    public void Configure(ISiloBuilder siloBuilder)
    {
        siloBuilder.AddMemoryGrainStorage("userStore");
    }
}

public class UserApiTests : IClassFixture<CustomWebApplicationFactory>
{
    private readonly CustomWebApplicationFactory _factory;
    private readonly HttpClient _client;

    public UserApiTests(CustomWebApplicationFactory factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task GetUser_WhenUserExists_ReturnsUserInfo()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var expectedUserInfo = new UserInfo(
            "Test User",
            "<EMAIL>",
            DateTime.UtcNow,
            DateTime.UtcNow
        );

        // First, create the user
        await _client.PostAsJsonAsync($"/api/users/{userId}", expectedUserInfo);

        // Act
        var response = await _client.GetAsync($"/api/users/{userId}");

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        var userInfo = JsonSerializer.Deserialize<UserInfo>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        Assert.NotNull(userInfo);
        Assert.Equal(expectedUserInfo.Name, userInfo.Name);
        Assert.Equal(expectedUserInfo.Email, userInfo.Email);
    }

    [Fact]
    public async Task UpdateUser_WhenCalled_UpdatesUserInfo()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var userInfo = new UserInfo(
            "Updated User",
            "<EMAIL>",
            DateTime.UtcNow,
            DateTime.UtcNow
        );

        // Act
        var response = await _client.PostAsJsonAsync($"/api/users/{userId}", userInfo);

        // Assert
        response.EnsureSuccessStatusCode();

        // Verify the update
        var getResponse = await _client.GetAsync($"/api/users/{userId}");
        getResponse.EnsureSuccessStatusCode();

        var content = await getResponse.Content.ReadAsStringAsync();
        var retrievedUserInfo = JsonSerializer.Deserialize<UserInfo>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        Assert.NotNull(retrievedUserInfo);
        Assert.Equal(userInfo.Name, retrievedUserInfo.Name);
        Assert.Equal(userInfo.Email, retrievedUserInfo.Email);
    }

    [Fact]
    public async Task UpdateUserName_WhenCalled_UpdatesOnlyName()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var initialUserInfo = new UserInfo(
            "Initial User",
            "<EMAIL>",
            DateTime.UtcNow,
            DateTime.UtcNow
        );

        await _client.PostAsJsonAsync($"/api/users/{userId}", initialUserInfo);

        var newName = "Updated Name Only";

        // Act
        var response = await _client.PutAsJsonAsync($"/api/users/{userId}/name", newName);

        // Assert
        response.EnsureSuccessStatusCode();

        // Verify the update
        var getResponse = await _client.GetAsync($"/api/users/{userId}");
        var content = await getResponse.Content.ReadAsStringAsync();
        var userInfo = JsonSerializer.Deserialize<UserInfo>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        Assert.NotNull(userInfo);
        Assert.Equal(newName, userInfo.Name);
        Assert.Equal(initialUserInfo.Email, userInfo.Email); // Email should remain unchanged
    }

    [Fact]
    public async Task GetWeatherForecast_WhenCalled_ReturnsWeatherData()
    {
        // Act
        var response = await _client.GetAsync("/weatherforecast");

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();

        Assert.NotNull(content);
        Assert.Contains("temperatureC", content);
    }
}
