﻿using Orleans.TestingHost;
using Curios.Grains.Interfaces;
using Curios.Grains;

namespace Curios.Grains.Tests;

public class UserGrainTests : IClassFixture<ClusterFixture>
{
    private readonly TestCluster _cluster;

    public UserGrainTests(ClusterFixture fixture)
    {
        _cluster = fixture.Cluster;
    }

    [Fact]
    public async Task GetNameAsync_WhenUserNotSet_ReturnsUnknown()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var userGrain = _cluster.GrainFactory.GetGrain<IUserGrain>(userId);

        // Act
        var name = await userGrain.GetNameAsync();

        // Assert
        Assert.Equal("Unknown", name);
    }

    [Fact]
    public async Task SetNameAsync_WhenCalled_UpdatesName()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var userGrain = _cluster.GrainFactory.GetGrain<IUserGrain>(userId);
        var expectedName = "John Doe";

        // Act
        await userGrain.SetNameAsync(expectedName);
        var actualName = await userGrain.GetNameAsync();

        // Assert
        Assert.Equal(expectedName, actualName);
    }

    [Fact]
    public async Task GetUserInfoAsync_WhenUserNotSet_ReturnsDefaultInfo()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var userGrain = _cluster.GrainFactory.GetGrain<IUserGrain>(userId);

        // Act
        var userInfo = await userGrain.GetUserInfoAsync();

        // Assert
        Assert.Equal("Unknown", userInfo.Name);
        Assert.Equal("", userInfo.Email);
        Assert.Equal(default, userInfo.CreatedAt);
        Assert.Equal(default, userInfo.LastUpdatedAt);
    }

    [Fact]
    public async Task UpdateUserInfoAsync_WhenCalled_UpdatesUserInfo()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var userGrain = _cluster.GrainFactory.GetGrain<IUserGrain>(userId);
        var expectedUserInfo = new UserInfo(
            "Jane Doe",
            "<EMAIL>",
            DateTime.UtcNow,
            DateTime.UtcNow
        );

        // Act
        await userGrain.UpdateUserInfoAsync(expectedUserInfo);
        var actualUserInfo = await userGrain.GetUserInfoAsync();

        // Assert
        Assert.Equal(expectedUserInfo.Name, actualUserInfo.Name);
        Assert.Equal(expectedUserInfo.Email, actualUserInfo.Email);
        Assert.True(actualUserInfo.CreatedAt > default(DateTime));
        Assert.True(actualUserInfo.LastUpdatedAt > default(DateTime));
    }

    [Fact]
    public async Task SetNameAsync_WhenCalledMultipleTimes_UpdatesLastModifiedTime()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var userGrain = _cluster.GrainFactory.GetGrain<IUserGrain>(userId);

        // Act
        await userGrain.SetNameAsync("First Name");
        var firstInfo = await userGrain.GetUserInfoAsync();

        await Task.Delay(100); // Ensure time difference

        await userGrain.SetNameAsync("Second Name");
        var secondInfo = await userGrain.GetUserInfoAsync();

        // Assert
        Assert.True(secondInfo.LastUpdatedAt > firstInfo.LastUpdatedAt);
        Assert.Equal("Second Name", secondInfo.Name);
    }
}
