﻿using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Orleans;
using Orleans.TestingHost;
using Orleans.Hosting;
using System.Net.Http.Json;
using System.Text.Json;
using Curios.Grains.Interfaces;
using Curios.Grains;

namespace Curios.Integration.Tests;

public class CustomWebApplicationFactory : WebApplicationFactory<Program>
{
    private TestCluster? _cluster;

    protected override IHost CreateHost(IHostBuilder builder)
    {
        // Create Orleans test cluster
        var clusterBuilder = new TestClusterBuilder();
        clusterBuilder.AddSiloBuilderConfigurator<TestSiloConfigurator>();
        _cluster = clusterBuilder.Build();
        _cluster.Deploy();

        // Configure the web host to use the test cluster
        builder.ConfigureServices(services =>
        {
            // Remove the existing Orleans client registration
            var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(Orleans.IClusterClient));
            if (descriptor != null)
            {
                services.Remove(descriptor);
            }

            // Add the test cluster client
            services.AddSingleton(_cluster.Client);
            services.AddSingleton<Orleans.IClusterClient>(_cluster.Client);
        });

        return base.CreateHost(builder);
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            _cluster?.StopAllSilos();
            _cluster?.Dispose();
        }
        base.Dispose(disposing);
    }
}

public class TestSiloConfigurator : ISiloConfigurator
{
    public void Configure(ISiloBuilder siloBuilder)
    {
        siloBuilder.AddMemoryGrainStorage("userStore");
    }
}

public class EndToEndTests : IClassFixture<CustomWebApplicationFactory>
{
    private readonly CustomWebApplicationFactory _factory;
    private readonly HttpClient _client;

    public EndToEndTests(CustomWebApplicationFactory factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task UserWorkflow_CreateUpdateRetrieve_WorksCorrectly()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var initialUserInfo = new UserInfo(
            "John Doe",
            "<EMAIL>",
            DateTime.UtcNow,
            DateTime.UtcNow
        );

        // Act & Assert - Create user
        var createResponse = await _client.PostAsJsonAsync($"/api/users/{userId}", initialUserInfo);
        createResponse.EnsureSuccessStatusCode();

        // Act & Assert - Retrieve user
        var getResponse = await _client.GetAsync($"/api/users/{userId}");
        getResponse.EnsureSuccessStatusCode();

        var content = await getResponse.Content.ReadAsStringAsync();
        var retrievedUserInfo = JsonSerializer.Deserialize<UserInfo>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        Assert.NotNull(retrievedUserInfo);
        Assert.Equal(initialUserInfo.Name, retrievedUserInfo.Name);
        Assert.Equal(initialUserInfo.Email, retrievedUserInfo.Email);

        // Act & Assert - Update user name only
        var newName = "Jane Doe";
        var updateNameResponse = await _client.PutAsJsonAsync($"/api/users/{userId}/name", newName);
        updateNameResponse.EnsureSuccessStatusCode();

        // Verify name update
        var getAfterNameUpdateResponse = await _client.GetAsync($"/api/users/{userId}");
        var nameUpdateContent = await getAfterNameUpdateResponse.Content.ReadAsStringAsync();
        var updatedUserInfo = JsonSerializer.Deserialize<UserInfo>(nameUpdateContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        Assert.NotNull(updatedUserInfo);
        Assert.Equal(newName, updatedUserInfo.Name);
        Assert.Equal(initialUserInfo.Email, updatedUserInfo.Email); // Email should remain unchanged

        // Act & Assert - Update full user info
        var finalUserInfo = new UserInfo(
            "Final Name",
            "<EMAIL>",
            DateTime.UtcNow,
            DateTime.UtcNow
        );

        var finalUpdateResponse = await _client.PostAsJsonAsync($"/api/users/{userId}", finalUserInfo);
        finalUpdateResponse.EnsureSuccessStatusCode();

        // Verify final update
        var finalGetResponse = await _client.GetAsync($"/api/users/{userId}");
        var finalContent = await finalGetResponse.Content.ReadAsStringAsync();
        var finalRetrievedUserInfo = JsonSerializer.Deserialize<UserInfo>(finalContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        Assert.NotNull(finalRetrievedUserInfo);
        Assert.Equal(finalUserInfo.Name, finalRetrievedUserInfo.Name);
        Assert.Equal(finalUserInfo.Email, finalRetrievedUserInfo.Email);
    }

    [Fact]
    public async Task MultipleUsers_CanBeCreatedAndRetrievedIndependently()
    {
        // Arrange
        var user1Id = Guid.NewGuid().ToString();
        var user2Id = Guid.NewGuid().ToString();

        var user1Info = new UserInfo("User One", "<EMAIL>", DateTime.UtcNow, DateTime.UtcNow);
        var user2Info = new UserInfo("User Two", "<EMAIL>", DateTime.UtcNow, DateTime.UtcNow);

        // Act - Create both users
        await _client.PostAsJsonAsync($"/api/users/{user1Id}", user1Info);
        await _client.PostAsJsonAsync($"/api/users/{user2Id}", user2Info);

        // Act - Retrieve both users
        var user1Response = await _client.GetAsync($"/api/users/{user1Id}");
        var user2Response = await _client.GetAsync($"/api/users/{user2Id}");

        // Assert
        user1Response.EnsureSuccessStatusCode();
        user2Response.EnsureSuccessStatusCode();

        var user1Content = await user1Response.Content.ReadAsStringAsync();
        var user2Content = await user2Response.Content.ReadAsStringAsync();

        var retrievedUser1 = JsonSerializer.Deserialize<UserInfo>(user1Content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });
        var retrievedUser2 = JsonSerializer.Deserialize<UserInfo>(user2Content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        Assert.NotNull(retrievedUser1);
        Assert.NotNull(retrievedUser2);
        Assert.Equal(user1Info.Name, retrievedUser1.Name);
        Assert.Equal(user2Info.Name, retrievedUser2.Name);
        Assert.Equal(user1Info.Email, retrievedUser1.Email);
        Assert.Equal(user2Info.Email, retrievedUser2.Email);
    }

    [Fact]
    public async Task HealthCheck_ReturnsHealthy()
    {
        // Act
        var response = await _client.GetAsync("/health");

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        Assert.Contains("Healthy", content);
    }
}
