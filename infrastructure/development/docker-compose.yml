version: '3.8'

# 开发环境 - 完整的分布式系统
# 用途：模拟生产环境的完整部署，包含多个实例和负载均衡
# 使用：docker-compose -f infrastructure/development/docker-compose.yml up -d

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:16-alpine
    container_name: curios-postgres-dev
    environment:
      POSTGRES_DB: orleansdb
      POSTGRES_USER: orleans
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-orleans123}
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./init-orleans-db.sql:/docker-entrypoint-initdb.d/init-orleans-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U orleans -d orleansdb"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis 集群
  redis:
    image: redis:7-alpine
    container_name: curios-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    restart: unless-stopped

  # Orleans Silo 实例 1
  silo1:
    image: ghcr.io/hxw/curios-api-silo:develop
    container_name: curios-silo-dev1
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__redis=redis:6379
      - ConnectionStrings__orleansdb=Host=postgres;Database=orleansdb;Username=orleans;Password=${POSTGRES_PASSWORD:-orleans123}
      - ORLEANS_SILO_NAME=silo-dev1
      - ORLEANS_CLUSTER_ID=curios-dev-cluster
      - ORLEANS_SERVICE_ID=curios-dev-service
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
    ports:
      - "11111:8080"
      - "30000:30000"
    restart: unless-stopped

  # Orleans Silo 实例 2
  silo2:
    image: ghcr.io/hxw/curios-api-silo:develop
    container_name: curios-silo-dev2
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__redis=redis:6379
      - ConnectionStrings__orleansdb=Host=postgres;Database=orleansdb;Username=orleans;Password=${POSTGRES_PASSWORD:-orleans123}
      - ORLEANS_SILO_NAME=silo-dev2
      - ORLEANS_CLUSTER_ID=curios-dev-cluster
      - ORLEANS_SERVICE_ID=curios-dev-service
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
      silo1:
        condition: service_started
    ports:
      - "11112:8080"
      - "30001:30000"
    restart: unless-stopped

  # API 实例 1
  api1:
    image: ghcr.io/hxw/curios-api-api:develop
    container_name: curios-api-dev1
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__redis=redis:6379
      - ORLEANS_CLUSTER_ID=curios-dev-cluster
      - ORLEANS_SERVICE_ID=curios-dev-service
    depends_on:
      - silo1
      - silo2
    ports:
      - "5001:8080"
    restart: unless-stopped

  # API 实例 2
  api2:
    image: ghcr.io/hxw/curios-api-api:develop
    container_name: curios-api-dev2
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__redis=redis:6379
      - ORLEANS_CLUSTER_ID=curios-dev-cluster
      - ORLEANS_SERVICE_ID=curios-dev-service
    depends_on:
      - silo1
      - silo2
    ports:
      - "5002:8080"
    restart: unless-stopped

  # 负载均衡器
  nginx:
    image: nginx:alpine
    container_name: curios-nginx-dev
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - api1
      - api2
    restart: unless-stopped

  # 管理工具 - Portainer
  portainer:
    image: portainer/portainer-ce:latest
    container_name: curios-portainer-dev
    ports:
      - "9000:9000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_dev_data:/data
    restart: unless-stopped
    profiles:
      - tools

  # 数据库管理工具 - PgAdmin
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: curios-pgadmin-dev
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_dev_data:/var/lib/pgadmin
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    profiles:
      - tools

  # Redis 管理工具 - Redis Commander
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: curios-redis-commander-dev
    environment:
      - REDIS_HOSTS=dev:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    profiles:
      - tools

volumes:
  postgres_dev_data:
    name: curios_postgres_dev_data
  redis_dev_data:
    name: curios_redis_dev_data
  portainer_dev_data:
    name: curios_portainer_dev_data
  pgadmin_dev_data:
    name: curios_pgadmin_dev_data

networks:
  default:
    name: curios-dev-network
