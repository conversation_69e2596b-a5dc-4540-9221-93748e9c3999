version: '3.8'

# 本地开发环境 - 基础设施组件
# 用途：为本地开发提供 PostgreSQL、Redis 等基础服务
# 使用：docker-compose -f infrastructure/local/docker-compose.yml up -d

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:16-alpine
    container_name: curios-postgres-local
    environment:
      POSTGRES_DB: orleansdb
      POSTGRES_USER: orleans
      POSTGRES_PASSWORD: orleans123
    ports:
      - "5432:5432"
    volumes:
      - postgres_local_data:/var/lib/postgresql/data
      - ./init-orleans-db.sql:/docker-entrypoint-initdb.d/init-orleans-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U orleans -d orleansdb"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis 缓存和集群
  redis:
    image: redis:7-alpine
    container_name: curios-redis-local
    ports:
      - "6379:6379"
    volumes:
      - redis_local_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    restart: unless-stopped

  # 数据库管理工具 - PgAdmin
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: curios-pgadmin-local
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_local_data:/var/lib/pgadmin
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    profiles:
      - tools

  # Redis 管理工具 - Redis Commander
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: curios-redis-commander-local
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    profiles:
      - tools

volumes:
  postgres_local_data:
    name: curios_postgres_local_data
  redis_local_data:
    name: curios_redis_local_data
  pgadmin_local_data:
    name: curios_pgadmin_local_data

networks:
  default:
    name: curios-local-network
