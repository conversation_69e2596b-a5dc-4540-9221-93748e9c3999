version: '3.8'

# 本地开发环境 - 监控组件
# 用途：为本地开发提供日志、追踪、监控等可观测性服务
# 使用：docker-compose -f infrastructure/local/docker-compose.monitoring.yml up -d

services:
  # 日志管理 - Seq
  seq:
    image: datalust/seq:latest
    container_name: curios-seq-local
    environment:
      - ACCEPT_EULA=Y
    ports:
      - "5341:80"
    volumes:
      - seq_local_data:/data
    restart: unless-stopped

  # 分布式追踪 - Jaeger
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: curios-jaeger-local
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # HTTP collector
      - "14250:14250"  # gRPC collector
      - "6831:6831/udp"  # UDP collector
    environment:
      - COLLECTOR_OTLP_ENABLED=true
      - SPAN_STORAGE_TYPE=memory
    restart: unless-stopped

  # 指标收集 - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: curios-prometheus-local
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_local_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  # 监控面板 - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: curios-grafana-local
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_local_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
    restart: unless-stopped
    depends_on:
      - prometheus

  # Redis 监控导出器
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: curios-redis-exporter-local
    ports:
      - "9121:9121"
    environment:
      - REDIS_ADDR=redis://host.docker.internal:6379
    restart: unless-stopped
    profiles:
      - exporters

  # PostgreSQL 监控导出器
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    container_name: curios-postgres-exporter-local
    ports:
      - "9187:9187"
    environment:
      - DATA_SOURCE_NAME=postgresql://orleans:<EMAIL>:5432/orleansdb?sslmode=disable
    restart: unless-stopped
    profiles:
      - exporters

volumes:
  seq_local_data:
    name: curios_seq_local_data
  prometheus_local_data:
    name: curios_prometheus_local_data
  grafana_local_data:
    name: curios_grafana_local_data

networks:
  default:
    name: curios-local-monitoring-network
