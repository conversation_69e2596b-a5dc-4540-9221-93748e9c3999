global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus 自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # .NET 应用监控 (本地开发时的应用程序)
  - job_name: 'curios-api-local'
    static_configs:
      - targets: ['host.docker.internal:5314']  # API 默认端口
    metrics_path: '/metrics'
    scrape_interval: 5s

  - job_name: 'curios-silo-local'
    static_configs:
      - targets: ['host.docker.internal:8080']  # Silo 健康检查端口
    metrics_path: '/metrics'
    scrape_interval: 5s

  # Redis 监控
  - job_name: 'redis-local'
    static_configs:
      - targets: ['redis-exporter:9121']

  # PostgreSQL 监控
  - job_name: 'postgres-local'
    static_configs:
      - targets: ['postgres-exporter:9187']
