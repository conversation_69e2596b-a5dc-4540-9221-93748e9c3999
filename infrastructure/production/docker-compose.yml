version: '3.8'

# 生产环境 - 高可用分布式系统
# 用途：生产环境部署，包含多个实例、负载均衡和监控
# 使用：docker-compose -f infrastructure/production/docker-compose.yml up -d

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:16-alpine
    container_name: curios-postgres-prod
    environment:
      POSTGRES_DB: orleansdb
      POSTGRES_USER: orleans
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-orleans123}
    ports:
      - "5432:5432"
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./init-orleans-db.sql:/docker-entrypoint-initdb.d/init-orleans-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U orleans -d orleansdb"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: postgres -c max_connections=200 -c shared_buffers=256MB
    restart: unless-stopped

  # Redis 集群
  redis:
    image: redis:7-alpine
    container_name: curios-redis-prod
    ports:
      - "6379:6379"
    volumes:
      - redis_prod_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: redis-server --appendonly yes --maxmemory 1gb --maxmemory-policy allkeys-lru
    restart: unless-stopped

  # Orleans Silo 实例 1
  silo1:
    image: ghcr.io/hxw/curios-api-silo:latest
    container_name: curios-silo-prod1
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__redis=redis:6379
      - ConnectionStrings__orleansdb=Host=postgres;Database=orleansdb;Username=orleans;Password=${POSTGRES_PASSWORD:-orleans123}
      - ORLEANS_SILO_NAME=silo-prod1
      - ORLEANS_CLUSTER_ID=curios-prod-cluster
      - ORLEANS_SERVICE_ID=curios-prod-service
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
    ports:
      - "11111:8080"
      - "30000:30000"
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Orleans Silo 实例 2
  silo2:
    image: ghcr.io/hxw/curios-api-silo:latest
    container_name: curios-silo-prod2
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__redis=redis:6379
      - ConnectionStrings__orleansdb=Host=postgres;Database=orleansdb;Username=orleans;Password=${POSTGRES_PASSWORD:-orleans123}
      - ORLEANS_SILO_NAME=silo-prod2
      - ORLEANS_CLUSTER_ID=curios-prod-cluster
      - ORLEANS_SERVICE_ID=curios-prod-service
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
      silo1:
        condition: service_started
    ports:
      - "11112:8080"
      - "30001:30000"
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Orleans Silo 实例 3
  silo3:
    image: ghcr.io/hxw/curios-api-silo:latest
    container_name: curios-silo-prod3
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__redis=redis:6379
      - ConnectionStrings__orleansdb=Host=postgres;Database=orleansdb;Username=orleans;Password=${POSTGRES_PASSWORD:-orleans123}
      - ORLEANS_SILO_NAME=silo-prod3
      - ORLEANS_CLUSTER_ID=curios-prod-cluster
      - ORLEANS_SERVICE_ID=curios-prod-service
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
      silo1:
        condition: service_started
    ports:
      - "11113:8080"
      - "30002:30000"
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # API 实例 1
  api1:
    image: ghcr.io/hxw/curios-api-api:latest
    container_name: curios-api-prod1
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__redis=redis:6379
      - ORLEANS_CLUSTER_ID=curios-prod-cluster
      - ORLEANS_SERVICE_ID=curios-prod-service
    depends_on:
      - silo1
      - silo2
      - silo3
    ports:
      - "5001:8080"
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # API 实例 2
  api2:
    image: ghcr.io/hxw/curios-api-api:latest
    container_name: curios-api-prod2
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__redis=redis:6379
      - ORLEANS_CLUSTER_ID=curios-prod-cluster
      - ORLEANS_SERVICE_ID=curios-prod-service
    depends_on:
      - silo1
      - silo2
      - silo3
    ports:
      - "5002:8080"
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # API 实例 3
  api3:
    image: ghcr.io/hxw/curios-api-api:latest
    container_name: curios-api-prod3
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__redis=redis:6379
      - ORLEANS_CLUSTER_ID=curios-prod-cluster
      - ORLEANS_SERVICE_ID=curios-prod-service
    depends_on:
      - silo1
      - silo2
      - silo3
    ports:
      - "5003:8080"
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # 负载均衡器
  nginx:
    image: nginx:alpine
    container_name: curios-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - api1
      - api2
      - api3
    restart: unless-stopped

  # 管理工具 - Portainer
  portainer:
    image: portainer/portainer-ce:latest
    container_name: curios-portainer-prod
    ports:
      - "9000:9000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_prod_data:/data
    restart: unless-stopped
    profiles:
      - tools

volumes:
  postgres_prod_data:
    name: curios_postgres_prod_data
  redis_prod_data:
    name: curios_redis_prod_data
  portainer_prod_data:
    name: curios_portainer_prod_data

networks:
  default:
    name: curios-prod-network
