# 🏗️ 基础设施组件分离指南

## 📋 概述

本指南介绍如何使用完全分离的基础设施组件和应用程序架构。程序和组件完全解耦，支持多环境配置。

## 🎯 设计目标

1. **程序与组件分离** - 应用程序和基础设施组件完全独立
2. **多环境支持** - 支持 local、development、production 环境
3. **配置驱动** - 所有连接信息通过配置管理
4. **统一管理** - 提供统一的脚本管理不同环境

## 📁 目录结构

```
curios-api/
├── infrastructure/           # 基础设施配置
│   ├── local/               # 本地开发环境
│   │   ├── docker-compose.yml
│   │   ├── docker-compose.monitoring.yml
│   │   └── prometheus.yml
│   ├── development/         # 开发环境
│   │   └── docker-compose.yml
│   └── production/          # 生产环境
│       └── docker-compose.yml
├── config/                  # 配置文件
│   └── environments/        # 环境配置
│       ├── local.env
│       ├── development.env
│       └── production.env
├── scripts/                 # 管理脚本
│   ├── infrastructure.sh    # 基础设施管理
│   └── run-app.sh          # 应用程序管理
└── src/                     # 应用程序代码
    ├── Curios.Api/
    └── Curios.Silo/
```

## 🚀 使用方式

### **1. 本地开发环境**

**启动基础设施：**
```bash
# 仅启动基础服务 (PostgreSQL + Redis)
./scripts/infrastructure.sh local

# 启动基础服务 + 监控
./scripts/infrastructure.sh local monitoring

# 启动基础服务 + 监控 + 管理工具
./scripts/infrastructure.sh local monitoring tools
```

**启动应用程序：**
```bash
# 启动完整应用 (Silo + API)
./scripts/run-app.sh start local

# 仅启动 Silo
./scripts/run-app.sh silo local

# 仅启动 API
./scripts/run-app.sh api local
```

**访问地址：**
- 🌐 **API**: http://localhost:5314
- 📖 **Swagger**: http://localhost:5314/swagger
- 📊 **Seq 日志**: http://localhost:5341
- 🔍 **Jaeger 追踪**: http://localhost:16686
- 📈 **Grafana 监控**: http://localhost:3000 (admin/admin)

### **2. 开发环境**

**启动完整环境：**
```bash
./scripts/infrastructure.sh development
```

**访问地址：**
- 🌐 **API**: http://localhost
- 📊 **Portainer**: http://localhost:9000

### **3. 生产环境**

**启动完整环境：**
```bash
./scripts/infrastructure.sh production
```

**访问地址：**
- 🌐 **API**: http://localhost
- 📊 **Portainer**: http://localhost:9000

## 🔧 配置说明

### **环境配置文件**

每个环境都有独立的配置文件：

- `config/environments/local.env` - 本地开发配置
- `config/environments/development.env` - 开发环境配置
- `config/environments/production.env` - 生产环境配置

### **应用程序配置**

应用程序通过 `appsettings.json` 和环境变量获取配置：

```json
{
  "ConnectionStrings": {
    "redis": "localhost:6379",
    "orleansdb": "Host=localhost;Database=orleansdb;Username=orleans;Password=**********"
  },
  "Orleans": {
    "ClusterId": "curios-local-cluster",
    "ServiceId": "curios-local-service"
  },
  "Logging": {
    "Seq": {
      "ServerUrl": "http://localhost:5341"
    }
  },
  "OpenTelemetry": {
    "Jaeger": {
      "Endpoint": "http://localhost:14268/api/traces"
    }
  }
}
```

## 📊 组件说明

### **基础设施组件**

| 组件 | 用途 | 本地端口 | 容器端口 |
|------|------|----------|----------|
| PostgreSQL | Orleans 数据存储 | 5432 | 5432 |
| Redis | Orleans 集群管理 | 6379 | 6379 |
| Seq | 日志管理 | 5341 | 80 |
| Jaeger | 分布式追踪 | 16686 | 16686 |
| Grafana | 监控面板 | 3000 | 3000 |
| Prometheus | 指标收集 | 9090 | 9090 |

### **管理工具**

| 工具 | 用途 | 访问地址 | 凭据 |
|------|------|----------|------|
| PgAdmin | PostgreSQL 管理 | http://localhost:8080 | <EMAIL>/admin123 |
| Redis Commander | Redis 管理 | http://localhost:8081 | - |
| Portainer | Docker 管理 | http://localhost:9000 | admin/admin |

## 🛠️ 常用操作

### **查看状态**
```bash
# 查看基础设施状态
./scripts/infrastructure.sh status local

# 查看应用程序状态
./scripts/run-app.sh status
```

### **查看日志**
```bash
# 查看基础设施日志
./scripts/infrastructure.sh logs local postgres

# 查看应用程序日志（通过 Seq）
# 访问 http://localhost:5341
```

### **停止服务**
```bash
# 停止应用程序
./scripts/run-app.sh stop

# 停止基础设施
./scripts/infrastructure.sh stop local
```

### **清理资源**
```bash
# 清理 Docker 资源
./scripts/infrastructure.sh cleanup
```

## 🔄 开发工作流

### **日常开发流程**
1. 启动基础设施：`./scripts/infrastructure.sh local monitoring`
2. 启动应用程序：`./scripts/run-app.sh start local`
3. 开发和测试
4. 停止应用程序：`./scripts/run-app.sh stop`
5. 停止基础设施：`./scripts/infrastructure.sh stop local`

### **调试流程**
1. 启动基础设施（包含监控）：`./scripts/infrastructure.sh local monitoring`
2. 在 IDE 中调试启动应用程序
3. 通过监控工具查看日志和追踪：
   - Seq: http://localhost:5341
   - Jaeger: http://localhost:16686
   - Grafana: http://localhost:3000

## 🚨 故障排除

### **常见问题**

**1. 端口冲突**
```bash
# 检查端口占用
lsof -i :5432  # PostgreSQL
lsof -i :6379  # Redis
lsof -i :5341  # Seq

# 停止冲突的服务
./scripts/infrastructure.sh stop local
```

**2. 数据库连接失败**
```bash
# 检查 PostgreSQL 状态
./scripts/infrastructure.sh status local

# 查看 PostgreSQL 日志
./scripts/infrastructure.sh logs local postgres
```

**3. Orleans 集群问题**
```bash
# 检查 Redis 状态
./scripts/infrastructure.sh logs local redis

# 重启应用程序
./scripts/run-app.sh restart local
```

## 📈 性能优化

### **本地开发优化**
- 仅启动必要的组件
- 使用 SSD 存储 Docker 卷
- 调整 Docker 资源限制

### **生产环境优化**
- 使用专用的数据库服务器
- 配置 Redis 持久化
- 启用 Nginx 缓存
- 配置日志轮转

## 🔐 安全考虑

### **开发环境**
- 使用默认密码（仅限开发）
- 禁用生产环境功能

### **生产环境**
- 使用强密码和环境变量
- 启用 HTTPS
- 配置防火墙规则
- 定期更新镜像
