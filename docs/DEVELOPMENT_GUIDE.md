# 🚀 开发指南 - Docker优先模式

## 🎯 开发理念

**主要方式**：Docker Compose（适合服务器开发）  
**备用方式**：Aspire（仅用于深度调试）

## 🛠️ 日常开发流程

### **1. 启动开发环境**

```bash
# 启动完整的开发环境
./deploy/scripts/start-dev.sh

# 这会启动：
# - Redis (缓存和Orleans集群)
# - PostgreSQL (数据持久化)
# - Orleans Silo (业务逻辑层)
# - API服务 (Web接口)
# - Nginx (负载均衡)
```

### **2. 查看服务状态**

```bash
# 快速查看所有服务状态
./scripts/monitor.sh status

# 输出示例：
# ✅ API服务健康
# ✅ PostgreSQL连接正常  
# ✅ Redis连接正常
```

### **3. 实时监控日志**

```bash
# 查看所有服务日志
./scripts/monitor.sh logs

# 查看特定服务日志
./scripts/monitor.sh logs api
./scripts/monitor.sh logs silo

# 查看错误日志
./scripts/monitor.sh errors

# 查看最近100行日志
./scripts/monitor.sh recent 100
```

### **4. 启动管理工具**

```bash
# 启动可视化管理工具
./scripts/monitor.sh tools

# 然后访问：
# - Portainer: http://localhost:9000 (Docker管理)
# - PgAdmin: http://localhost:8080 (数据库管理)
# - Redis Commander: http://localhost:8081 (Redis管理)
```

## 🔧 代码开发流程

### **修改代码后的更新流程**

```bash
# 1. 修改代码后，重新构建并重启服务
cd deploy/docker
docker-compose build api silo  # 只构建修改的服务
docker-compose up -d          # 重启服务

# 2. 或者使用监控脚本重启
./scripts/monitor.sh restart api
./scripts/monitor.sh restart silo
```

### **调试特定问题**

```bash
# 查看容器内部
docker exec -it curios-api-dev bash
docker exec -it curios-silo-dev bash

# 查看实时资源使用
docker stats

# 查看网络连接
docker network ls
docker network inspect curios-api-dev_default
```

## 🐛 深度调试（使用Aspire）

### **何时使用Aspire**
- 🔍 需要分布式追踪时
- 📊 需要详细性能指标时  
- 🚨 复杂问题需要可视化分析时
- 🔗 需要查看服务依赖关系时

### **启动Aspire调试**

```bash
# 1. 停止Docker环境（避免端口冲突）
cd deploy/docker
docker-compose down

# 2. 启动Aspire
dotnet run --project src/Curios.AppHost

# 3. 访问Dashboard
# 浏览器会自动打开，或访问显示的URL
# 通常是 https://localhost:15888
```

### **Aspire Dashboard功能**
- 📈 **实时指标** - CPU、内存、请求数
- 🔍 **分布式追踪** - 请求在各服务间的流转
- 📝 **日志聚合** - 所有服务日志统一查看
- 🌐 **服务地图** - 可视化服务依赖关系
- ⚡ **性能分析** - 识别性能瓶颈

## 📊 开发环境对比

### **Docker模式（日常使用）**
```
优势：
✅ 接近生产环境
✅ 容器隔离，环境一致
✅ 支持多实例测试
✅ 便于服务器开发
✅ 资源使用可控

劣势：
❌ 调试相对复杂
❌ 日志分散在各容器
❌ 缺少可视化监控
```

### **Aspire模式（调试使用）**
```
优势：
✅ 统一的监控面板
✅ 实时日志聚合
✅ 分布式追踪
✅ 性能指标详细
✅ 调试体验优秀

劣势：
❌ 仅适合本地开发
❌ 与生产环境差异大
❌ 资源消耗较高
```

## 🎯 最佳实践

### **日常开发建议**

1. **主要使用Docker**
   ```bash
   # 启动环境
   ./deploy/scripts/start-dev.sh
   
   # 监控状态
   ./scripts/monitor.sh status
   
   # 查看日志
   ./scripts/monitor.sh logs api
   ```

2. **代码修改后**
   ```bash
   # 重新构建和部署
   docker-compose build [service]
   docker-compose up -d
   
   # 验证更改
   ./scripts/monitor.sh health
   ```

3. **遇到问题时**
   ```bash
   # 查看错误日志
   ./scripts/monitor.sh errors
   
   # 重启有问题的服务
   ./scripts/monitor.sh restart [service]
   
   # 如果问题复杂，切换到Aspire调试
   ```

### **调试问题的层次**

1. **Level 1: 快速检查**
   ```bash
   ./scripts/monitor.sh status
   ./scripts/monitor.sh health
   ```

2. **Level 2: 日志分析**
   ```bash
   ./scripts/monitor.sh logs
   ./scripts/monitor.sh errors
   ```

3. **Level 3: 容器调试**
   ```bash
   docker exec -it [container] bash
   docker stats
   ```

4. **Level 4: Aspire深度调试**
   ```bash
   # 停止Docker，启动Aspire
   docker-compose down
   dotnet run --project src/Curios.AppHost
   ```

## 🚀 快速命令参考

### **常用Docker命令**
```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f [service]

# 重启服务
docker-compose restart [service]

# 重新构建
docker-compose build [service]

# 停止所有服务
docker-compose down

# 清理资源
docker system prune -f
```

### **常用监控命令**
```bash
# 服务状态
./scripts/monitor.sh status

# 实时日志
./scripts/monitor.sh logs [service]

# 健康检查
./scripts/monitor.sh health

# 重启服务
./scripts/monitor.sh restart [service]

# 启动管理工具
./scripts/monitor.sh tools
```

## 🎯 总结

这种**Docker优先，Aspire备用**的模式让你能够：

- 🚀 **高效开发** - 使用接近生产的环境
- 🔧 **灵活调试** - 需要时切换到Aspire
- 📊 **完整监控** - 通过脚本和管理工具
- 🎯 **专注业务** - 减少基础设施的干扰

开始你的开发之旅吧！
