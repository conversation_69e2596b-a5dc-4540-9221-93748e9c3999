# 🔐 第三方认证服务配置指南

本文档详细说明如何配置 Google OAuth 和 Apple Sign-In 认证服务。

## 📋 目录

- [Google OAuth 配置](#google-oauth-配置)
- [Apple Sign-In 配置](#apple-sign-in-配置)
- [应用配置](#应用配置)
- [测试验证](#测试验证)

## 🔵 Google OAuth 配置

### 1. 创建 Google Cloud 项目

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 Google+ API 和 Google Identity API

### 2. 配置 OAuth 同意屏幕

1. 在左侧菜单中选择 **APIs & Services** > **OAuth consent screen**
2. 选择用户类型（内部或外部）
3. 填写应用信息：
   - **应用名称**: Curios
   - **用户支持邮箱**: 你的邮箱
   - **开发者联系信息**: 你的邮箱
4. 添加授权域名（如果有）
5. 添加范围：
   - `email`
   - `profile`
   - `openid`

### 3. 创建 OAuth 客户端 ID

1. 在左侧菜单中选择 **APIs & Services** > **Credentials**
2. 点击 **Create Credentials** > **OAuth client ID**
3. 选择应用类型：
   - **Web application** (用于 Web 应用)
   - **iOS** (用于 iOS 应用)
   - **Android** (用于 Android 应用)

#### Web 应用配置
- **名称**: Curios Web Client
- **授权的 JavaScript 来源**: 
  - `http://localhost:3000` (开发环境)
  - `https://yourdomain.com` (生产环境)
- **授权的重定向 URI**:
  - `http://localhost:3000/auth/google/callback`
  - `https://yourdomain.com/auth/google/callback`

#### 移动应用配置
- **名称**: Curios Mobile Client
- **Bundle ID** (iOS): `com.yourcompany.curios`
- **Package name** (Android): `com.yourcompany.curios`
- **SHA-1 证书指纹** (Android): 从 keystore 获取

### 4. 获取客户端凭据

创建完成后，你将获得：
- **Client ID**: `*********-abcdefghijklmnop.apps.googleusercontent.com`
- **Client Secret**: `GOCSPX-abcdefghijklmnopqrstuvwxyz` (仅 Web 应用需要)

## 🍎 Apple Sign-In 配置

### 1. 注册 Apple Developer 账户

1. 访问 [Apple Developer](https://developer.apple.com/)
2. 注册或登录开发者账户（需要付费）

### 2. 创建 App ID

1. 在 **Certificates, Identifiers & Profiles** 中选择 **Identifiers**
2. 点击 **+** 创建新的 App ID
3. 选择 **App IDs**
4. 填写信息：
   - **Description**: Curios App
   - **Bundle ID**: `com.yourcompany.curios`
5. 在 **Capabilities** 中启用 **Sign In with Apple**

### 3. 创建 Services ID (用于 Web)

1. 在 **Identifiers** 中点击 **+**
2. 选择 **Services IDs**
3. 填写信息：
   - **Description**: Curios Web Service
   - **Identifier**: `com.yourcompany.curios.web`
4. 启用 **Sign In with Apple**
5. 点击 **Configure**：
   - **Primary App ID**: 选择之前创建的 App ID
   - **Web Domain**: `yourdomain.com`
   - **Return URLs**: `https://yourdomain.com/auth/apple/callback`

### 4. 创建私钥

1. 在 **Keys** 中点击 **+**
2. 填写 **Key Name**: Curios Apple Sign-In Key
3. 启用 **Sign In with Apple**
4. 点击 **Configure** 并选择你的 Primary App ID
5. 下载私钥文件 (`.p8` 文件) - **只能下载一次，请妥善保存**
6. 记录 **Key ID** (10 位字符)

### 5. 获取 Team ID

1. 在 Apple Developer 账户页面右上角查看 **Team ID**
2. 这是一个 10 位的字符串

## ⚙️ 应用配置

### 配置文件设置

在 `appsettings.json` 中添加以下配置：

```json
{
  "Authentication": {
    "Google": {
      "ClientId": "your-google-client-id.apps.googleusercontent.com",
      "ClientSecret": "your-google-client-secret"
    },
    "Apple": {
      "ClientId": "com.yourcompany.curios.web",
      "TeamId": "YOUR_TEAM_ID",
      "KeyId": "YOUR_KEY_ID",
      "PrivateKey": "-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_CONTENT\n-----END PRIVATE KEY-----"
    }
  },
  "Email": {
    "Smtp": {
      "Host": "smtp.gmail.com",
      "Port": 587,
      "Username": "<EMAIL>",
      "Password": "your-app-password"
    },
    "FromAddress": "<EMAIL>",
    "FromName": "Curios"
  }
}
```

### 环境变量设置

为了安全起见，建议使用环境变量：

```bash
# Google OAuth
export Authentication__Google__ClientId="your-google-client-id"
export Authentication__Google__ClientSecret="your-google-client-secret"

# Apple Sign-In
export Authentication__Apple__ClientId="com.yourcompany.curios.web"
export Authentication__Apple__TeamId="YOUR_TEAM_ID"
export Authentication__Apple__KeyId="YOUR_KEY_ID"
export Authentication__Apple__PrivateKey="-----BEGIN PRIVATE KEY-----..."

# Email SMTP
export Email__Smtp__Host="smtp.gmail.com"
export Email__Smtp__Port="587"
export Email__Smtp__Username="<EMAIL>"
export Email__Smtp__Password="your-app-password"
export Email__FromAddress="<EMAIL>"
```

## 📧 邮件服务配置

### Gmail SMTP 配置

1. 启用 2FA (两步验证)
2. 生成应用专用密码：
   - 访问 [Google 账户设置](https://myaccount.google.com/)
   - 选择 **安全性** > **应用专用密码**
   - 生成新的应用密码
3. 使用应用密码作为 SMTP 密码

### 其他 SMTP 服务

- **Outlook/Hotmail**: `smtp-mail.outlook.com:587`
- **Yahoo**: `smtp.mail.yahoo.com:587`
- **SendGrid**: `smtp.sendgrid.net:587`
- **Mailgun**: `smtp.mailgun.org:587`

## 🧪 测试验证

### 1. Google OAuth 测试

```bash
# 测试 Google Token 验证
curl -X POST "http://localhost:5314/api/auth/google/login" \
  -H "Content-Type: application/json" \
  -d '{"idToken": "your-google-id-token"}'
```

### 2. Apple Sign-In 测试

```bash
# 测试 Apple Token 验证
curl -X POST "http://localhost:5314/api/auth/apple/login" \
  -H "Content-Type: application/json" \
  -d '{
    "identityToken": "your-apple-identity-token",
    "authorizationCode": "your-apple-auth-code"
  }'
```

### 3. 邮件发送测试

```bash
# 测试邮件发送
curl -X POST "http://localhost:5314/api/auth/email/send-verification" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

## 🔒 安全注意事项

1. **永远不要在客户端代码中暴露 Client Secret**
2. **使用 HTTPS** 进行所有认证相关的通信
3. **定期轮换密钥和令牌**
4. **验证 JWT 签名** 以确保令牌的真实性
5. **实施速率限制** 防止暴力攻击
6. **记录所有认证事件** 用于安全审计

## 📱 移动应用集成

### iOS 集成

```swift
// 使用 GoogleSignIn SDK
import GoogleSignIn

// 配置 Google Sign-In
guard let path = Bundle.main.path(forResource: "GoogleService-Info", ofType: "plist"),
      let plist = NSDictionary(contentsOfFile: path),
      let clientId = plist["CLIENT_ID"] as? String else { return }

GIDSignIn.sharedInstance.configuration = GIDConfiguration(clientID: clientId)
```

### Android 集成

```kotlin
// 使用 Google Sign-In SDK
val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
    .requestIdToken(getString(R.string.default_web_client_id))
    .requestEmail()
    .build()

val googleSignInClient = GoogleSignIn.getClient(this, gso)
```

## 🆘 常见问题

### Google OAuth 问题

**Q: 收到 "invalid_client" 错误**
A: 检查 Client ID 是否正确，确保在 Google Console 中正确配置了授权域名

**Q: Token 验证失败**
A: 确保服务器时间同步，检查 Token 是否过期

### Apple Sign-In 问题

**Q: 收到 "invalid_client" 错误**
A: 检查 Services ID 配置，确保 Return URLs 正确

**Q: 私钥验证失败**
A: 确保私钥格式正确，包含完整的 BEGIN/END 标记

### 邮件发送问题

**Q: SMTP 认证失败**
A: 检查用户名密码，确保启用了应用专用密码

**Q: 邮件被标记为垃圾邮件**
A: 配置 SPF、DKIM 和 DMARC 记录，使用专业的邮件服务

## 📞 技术支持

如果遇到配置问题，请：

1. 检查日志文件中的详细错误信息
2. 验证所有配置参数是否正确
3. 确保网络连接正常
4. 查看相关服务的状态页面

更多帮助请参考：
- [Google OAuth 文档](https://developers.google.com/identity/protocols/oauth2)
- [Apple Sign-In 文档](https://developer.apple.com/sign-in-with-apple/)
- [项目 GitHub Issues](https://github.com/yourrepo/curios/issues)
