# 📖 Curios API 文档

## 🚀 快速开始

### 启动应用

```bash
# 启动 Aspire 应用（包含所有服务）
dotnet run --project src/Curios.AppHost
```

### 查看 API 文档

启动应用后，你可以通过以下方式查看 API 文档：

## 📋 API 文档访问方式

### 1. **Swagger UI（推荐）**

**URL**: http://localhost:5314/swagger

**功能**:
- 📖 交互式 API 文档
- 🧪 直接在浏览器中测试 API
- 📝 查看请求/响应模型
- 🔐 支持 JWT Token 认证测试
- 📥 下载 OpenAPI 规范文件

### 2. **OpenAPI JSON 规范**

**URL**: http://localhost:5314/openapi/v1.json

**用途**:
- 导入到 Postman、Insomnia 等工具
- 生成客户端 SDK
- 集成到其他开发工具

### 3. **健康检查端点**

**基本健康检查**: http://localhost:5314/api/health
**详细健康检查**: http://localhost:5314/api/health/detailed

## 🔐 API 认证

### JWT Token 使用方式

1. **获取 Token**：通过登录端点获取 JWT Token
2. **使用 Token**：在请求头中添加 `Authorization: Bearer {token}`

### Swagger 中测试认证

1. 点击 Swagger UI 右上角的 🔒 **Authorize** 按钮
2. 输入 `Bearer {your_jwt_token}`
3. 点击 **Authorize**
4. 现在可以测试需要认证的端点

## 📚 API 端点概览

### 认证相关 (`/api/auth`)

| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| POST | `/api/auth/email/send-verification` | 发送邮箱验证码 | ❌ |
| POST | `/api/auth/email/register` | 完成邮箱注册 | ❌ |
| POST | `/api/auth/email/login` | 邮箱密码登录 | ❌ |
| POST | `/api/auth/google/login` | Google OAuth 登录 | ❌ |
| POST | `/api/auth/apple/login` | Apple Sign-In 登录 | ❌ |
| POST | `/api/auth/validate-token` | 验证 JWT Token | ❌ |

### 用户管理 (`/api/users`)

| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| GET | `/api/users/{userId}` | 获取用户信息 | ✅ |
| PUT | `/api/users/{userId}` | 更新用户信息 | ✅ |
| POST | `/api/users/{userId}/deactivate` | 停用用户 | ✅ |
| POST | `/api/users/{userId}/activate` | 激活用户 | ✅ |
| DELETE | `/api/users/{userId}` | 删除用户 | ✅ |
| POST | `/api/users/{userId}/verify-email` | 验证邮箱 | ✅ |
| GET | `/api/users/{userId}/auth-providers/{type}` | 获取认证提供商 | ✅ |

### 健康检查 (`/api/health`)

| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| GET | `/api/health` | 基本健康检查 | ❌ |
| GET | `/api/health/detailed` | 详细健康检查 | ❌ |

## 🧪 测试示例

### 1. 邮箱注册流程

```bash
# 1. 发送验证码
curl -X POST "http://localhost:5314/api/auth/email/send-verification" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'

# 2. 完成注册
curl -X POST "http://localhost:5314/api/auth/email/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "verificationCode": "123456",
    "password": "SecurePassword123!",
    "displayName": "Test User"
  }'
```

### 2. 登录获取 Token

```bash
curl -X POST "http://localhost:5314/api/auth/email/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePassword123!"
  }'
```

### 3. 使用 Token 访问受保护端点

```bash
curl -X GET "http://localhost:5314/api/users/{userId}" \
  -H "Authorization: Bearer {your_jwt_token}"
```

## 📄 HTTP 测试文件

项目中包含了完整的 HTTP 测试文件：`src/Curios.Api/Curios.ApiService.http`

可以在 VS Code 中使用 REST Client 扩展直接运行这些测试。

## 🔧 开发工具集成

### Postman
1. 导入 OpenAPI 规范：http://localhost:5314/openapi/v1.json
2. 设置环境变量：`baseUrl = http://localhost:5314`
3. 配置认证：Bearer Token

### Insomnia
1. 导入 OpenAPI 规范
2. 配置基础 URL 和认证

### VS Code REST Client
使用项目中的 `.http` 文件直接测试 API

## 🚨 注意事项

- 🔒 需要认证的端点必须在请求头中包含有效的 JWT Token
- 📧 邮箱验证码在开发环境下会在日志中显示（生产环境需要配置 SMTP）
- 🌐 Google/Apple 登录需要配置相应的 OAuth 客户端（参见配置文档）
- ⏰ JWT Token 默认 24 小时过期

## 📞 支持

如有问题，请查看：
- Swagger UI 中的详细 API 文档
- 项目中的 HTTP 测试文件
- 健康检查端点的系统状态
