# 🚀 Aspire 使用指南

## 🎯 Aspire 在不同环境中的角色

### **本地开发环境**
- ✅ **主角** - 完整的Aspire体验
- ✅ **Dashboard** - 实时监控所有服务
- ✅ **自动编排** - Redis、PostgreSQL自动启动
- ✅ **日志聚合** - 统一查看所有服务日志

### **服务器部署环境**
- ❌ **不参与** - 使用Docker Compose部署
- ❌ **无Dashboard** - 需要其他监控方案
- ✅ **配置复用** - 连接字符串等配置可以复用

## 🖥️ 如何查看 Aspire Dashboard

### **方法1：本地开发时启动**

```bash
# 启动 Aspire AppHost
dotnet run --project src/Curios.AppHost

# Dashboard 会自动打开，通常在：
# https://localhost:15888
# 或者查看控制台输出的具体地址
```

### **方法2：独立启动 Dashboard**

```bash
# 安装 Aspire Dashboard 工具
dotnet tool install -g Microsoft.Extensions.Hosting.Aspire.Dashboard

# 启动独立的 Dashboard
aspire-dashboard

# 访问 http://localhost:18888
```

### **方法3：Docker 方式启动 Dashboard**

```bash
# 使用 Docker 运行 Aspire Dashboard
docker run -d \
  --name aspire-dashboard \
  -p 18888:18888 \
  -e ASPNETCORE_URLS=http://+:18888 \
  mcr.microsoft.com/dotnet/aspire-dashboard:9.0
```

## 📊 Dashboard 功能展示

### **服务概览**
- 🟢 服务状态（运行/停止/错误）
- 📈 资源使用情况（CPU、内存）
- 🔗 服务依赖关系图
- 🌐 端点和健康检查状态

### **日志查看**
- 📝 实时日志流
- 🔍 日志搜索和过滤
- 📊 日志级别统计
- 🎯 按服务分组查看

### **指标监控**
- 📈 HTTP 请求统计
- ⏱️ 响应时间分布
- 🔢 Orleans Grain 调用统计
- 💾 数据库连接池状态

### **分布式追踪**
- 🔍 请求链路追踪
- ⏱️ 每个步骤的耗时
- 🚨 错误和异常定位
- 📊 性能瓶颈分析

## 🔧 服务器环境的监控替代方案

由于服务器部署不使用Aspire，我们需要其他监控方案：

### **方案1：Portainer（推荐）**

```yaml
# 添加到 docker-compose.yml
portainer:
  image: portainer/portainer-ce:latest
  container_name: portainer
  ports:
    - "9000:9000"
  volumes:
    - /var/run/docker.sock:/var/run/docker.sock
    - portainer_data:/data
  restart: unless-stopped
```

**功能：**
- 🐳 Docker 容器管理
- 📊 资源使用监控
- 📝 日志查看
- 🔧 容器操作（重启、停止等）

### **方案2：Grafana + Prometheus**

```yaml
# 监控栈
prometheus:
  image: prom/prometheus
  ports:
    - "9090:9090"
  volumes:
    - ./prometheus.yml:/etc/prometheus/prometheus.yml

grafana:
  image: grafana/grafana
  ports:
    - "3000:3000"
  environment:
    - GF_SECURITY_ADMIN_PASSWORD=admin
```

**功能：**
- 📈 详细的指标监控
- 📊 自定义仪表板
- 🚨 告警通知
- 📉 历史数据分析

### **方案3：简单的日志查看**

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f api

# 查看最近的日志
docker-compose logs --tail=100 -f

# 查看错误日志
docker-compose logs | grep ERROR
```

## 🚀 推荐的混合方案

### **开发阶段**
1. **使用 Aspire AppHost** 进行本地开发
2. **查看 Dashboard** 监控服务状态
3. **利用日志聚合** 调试问题
4. **使用分布式追踪** 优化性能

### **部署阶段**
1. **Docker Compose** 部署到服务器
2. **Portainer** 提供基础监控
3. **传统日志命令** 查看问题
4. **考虑 Grafana** 用于生产监控

## 📝 实际操作示例

### **启动本地开发环境**

```bash
# 1. 启动 Aspire（推荐）
dotnet run --project src/Curios.AppHost

# Dashboard 自动打开，你可以看到：
# - Redis 容器状态
# - PostgreSQL 容器状态  
# - Silo 服务状态
# - API 服务状态
# - 实时日志流
# - 性能指标
```

### **部署到服务器后查看状态**

```bash
# SSH 到服务器
ssh <EMAIL>

# 查看服务状态
cd /opt/curios-api-dev  # 或 /opt/curios-api-prod
docker-compose ps

# 查看日志
docker-compose logs -f

# 查看特定服务
docker-compose logs -f api
docker-compose logs -f silo
```

## 🎯 最佳实践建议

### **开发时**
- ✅ 始终使用 Aspire AppHost
- ✅ 利用 Dashboard 监控服务
- ✅ 使用分布式追踪调试问题
- ✅ 关注性能指标

### **部署时**
- ✅ 使用 Docker Compose 部署
- ✅ 配置 Portainer 进行基础监控
- ✅ 设置日志轮转避免磁盘满
- ✅ 考虑专业监控方案（生产环境）

### **监控策略**
- 🔍 **开发环境**：Aspire Dashboard
- 🐳 **测试环境**：Portainer + Docker logs
- 📊 **生产环境**：Grafana + Prometheus + 告警

这样你就能在开发时享受 Aspire 的便利，在部署时使用成熟的 Docker 方案！
