# ⚡ 服务器快速配置指南

## 🎯 针对现有Nginx服务器的配置

### **第一步：配置Nginx反向代理**

```bash
# 1. 复制配置文件到服务器
sudo cp deploy/nginx/curios-sites.conf /etc/nginx/sites-available/curios

# 2. 编辑配置文件，替换域名
sudo nano /etc/nginx/sites-available/curios
# 将 dev.yourdomain.com 替换为你的实际域名
# 将 yourdomain.com 替换为你的生产域名

# 3. 启用站点
sudo ln -s /etc/nginx/sites-available/curios /etc/nginx/sites-enabled/

# 4. 测试配置
sudo nginx -t

# 5. 重载Nginx
sudo systemctl reload nginx
```

### **第二步：启动应用服务**

```bash
# 开发环境（默认不启动容器Nginx）
./deploy/scripts/start-dev.sh

# 或者如果你想用容器Nginx（端口8080）
./deploy/scripts/start-dev.sh --with-nginx
```

### **第三步：验证配置**

```bash
# 检查服务状态
./scripts/monitor.sh status

# 健康检查（会自动检测可用端口）
./scripts/monitor.sh health

# 测试API
curl http://your-domain.com/weatherforecast
```

## 🔧 端口配置说明

### **默认端口分配**
```
5001  - API服务直接端口（供Nginx代理）
8080  - 容器Nginx端口（如果启用）
9000  - Portainer管理界面
11111 - Orleans Silo管理界面
6379  - Redis
5432  - PostgreSQL
```

### **Nginx代理配置**
```nginx
# 开发环境
server {
    listen 80;
    server_name dev.yourdomain.com;
    
    location / {
        proxy_pass http://localhost:5001;  # 代理到API服务
        # ... 其他配置
    }
}

# 生产环境（负载均衡）
upstream curios_api_backend {
    server localhost:5002;  # API实例1
    server localhost:5003;  # API实例2
}

server {
    listen 80;
    server_name yourdomain.com;
    
    location / {
        proxy_pass http://curios_api_backend;
        # ... 其他配置
    }
}
```

## 🚀 部署流程

### **开发环境部署**
```bash
# 1. 启动服务（无容器Nginx）
./deploy/scripts/start-dev.sh

# 2. 验证服务
./scripts/monitor.sh health

# 3. 查看日志
./scripts/monitor.sh logs

# 4. 启动管理工具（可选）
./scripts/monitor.sh tools
```

### **生产环境部署**
```bash
# 1. 启动生产服务
./deploy/scripts/start-prod.sh

# 2. 验证负载均衡
curl http://yourdomain.com/weatherforecast

# 3. 监控服务状态
./scripts/monitor.sh status
```

## 🐛 故障排查

### **常见问题**

#### **1. 端口冲突**
```bash
# 检查端口占用
sudo netstat -tlnp | grep :80
sudo netstat -tlnp | grep :5001

# 如果80端口被占用，使用容器Nginx的8080端口
./deploy/scripts/start-dev.sh --with-nginx
```

#### **2. Nginx配置错误**
```bash
# 测试Nginx配置
sudo nginx -t

# 查看Nginx错误日志
sudo tail -f /var/log/nginx/error.log

# 重载配置
sudo systemctl reload nginx
```

#### **3. 服务无法访问**
```bash
# 检查Docker服务状态
./scripts/monitor.sh status

# 检查API直接访问
curl http://localhost:5001/weatherforecast

# 检查Nginx代理
curl -H "Host: dev.yourdomain.com" http://localhost/weatherforecast
```

#### **4. 数据库连接问题**
```bash
# 检查PostgreSQL状态
docker exec curios-postgres-dev pg_isready -U orleans

# 查看数据库日志
docker logs curios-postgres-dev

# 手动连接测试
docker exec -it curios-postgres-dev psql -U orleans -d orleansdb
```

## 📊 监控和维护

### **日常监控命令**
```bash
# 服务状态概览
./scripts/monitor.sh status

# 实时日志
./scripts/monitor.sh logs

# 健康检查
./scripts/monitor.sh health

# 重启服务
./scripts/monitor.sh restart api
```

### **性能监控**
```bash
# 查看资源使用
docker stats

# 查看系统负载
htop

# 查看磁盘使用
df -h
```

### **日志管理**
```bash
# 查看最近日志
./scripts/monitor.sh recent 100

# 查看错误日志
./scripts/monitor.sh errors

# 清理旧日志
docker system prune -f
```

## 🎯 最佳实践

### **安全配置**
1. **限制管理界面访问** - 只允许内网IP
2. **使用SSL证书** - 生产环境必须HTTPS
3. **定期更新** - 保持系统和容器镜像最新
4. **备份数据** - 定期备份PostgreSQL数据

### **性能优化**
1. **启用Gzip压缩** - Nginx配置gzip
2. **设置缓存头** - 静态资源缓存
3. **连接池优化** - 数据库连接池配置
4. **负载均衡** - 生产环境多实例部署

### **监控告警**
1. **健康检查** - 定期执行健康检查
2. **日志监控** - 监控错误日志
3. **资源监控** - CPU、内存、磁盘使用
4. **业务监控** - API响应时间和成功率

## ✅ 配置检查清单

### **Nginx配置**
- [ ] 配置文件已复制并编辑
- [ ] 域名已正确替换
- [ ] 站点已启用
- [ ] 配置测试通过
- [ ] Nginx已重载

### **应用服务**
- [ ] Docker服务已启动
- [ ] 端口映射正确
- [ ] 健康检查通过
- [ ] API可以访问

### **域名解析**
- [ ] DNS记录已配置
- [ ] 域名可以解析
- [ ] 通过域名可以访问API

### **监控工具**
- [ ] 监控脚本可以正常运行
- [ ] 管理工具可以访问
- [ ] 日志可以正常查看

完成以上配置后，你的Curios API就可以与现有Nginx完美整合了！
