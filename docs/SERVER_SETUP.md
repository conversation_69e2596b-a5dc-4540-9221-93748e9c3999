# 🖥️ 服务器配置指南

## 📊 服务器规划

### 方案选择：分离部署
- **开发服务器**：`dev.yourdomain.com` (可以是较小配置)
- **生产服务器**：`yourdomain.com` (推荐配置)

### 推荐配置

#### 开发服务器 (最小配置)
- **CPU**: 2核
- **内存**: 4GB RAM
- **存储**: 40GB SSD
- **带宽**: 5Mbps
- **月费用**: $20-40

#### 生产服务器 (推荐配置)
- **CPU**: 4核
- **内存**: 8GB RAM
- **存储**: 80GB SSD
- **带宽**: 10Mbps
- **月费用**: $40-80

## 🔧 服务器初始化配置

### 1. 基础环境安装

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要工具
sudo apt install -y curl wget git htop

# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 启动 Docker 服务
sudo systemctl start docker
sudo systemctl enable docker

# 将用户添加到 docker 组
sudo usermod -aG docker $USER
```

### 2. 创建项目目录

#### 开发服务器
```bash
sudo mkdir -p /opt/curios-api-dev
sudo chown $USER:$USER /opt/curios-api-dev
cd /opt/curios-api-dev

# 创建必要的配置文件
mkdir -p deploy/docker
```

#### 生产服务器
```bash
sudo mkdir -p /opt/curios-api-prod
sudo chown $USER:$USER /opt/curios-api-prod
cd /opt/curios-api-prod

# 创建必要的配置文件
mkdir -p deploy/docker
```

### 3. 配置文件部署

需要将以下文件复制到服务器：

#### 开发服务器文件
```
/opt/curios-api-dev/
├── docker-compose.yml (来自 deploy/docker/docker-compose.dev.yml)
├── nginx.dev.conf
└── init-orleans-db.sql
```

#### 生产服务器文件
```
/opt/curios-api-prod/
├── docker-compose.yml (来自 deploy/docker/docker-compose.yml)
├── nginx.prod.conf
└── init-orleans-db.sql
```

### 4. 环境变量配置

#### 开发服务器
```bash
# 创建 .env 文件
cat > /opt/curios-api-dev/.env << EOF
ASPNETCORE_ENVIRONMENT=Development
POSTGRES_PASSWORD=orleans123
EOF
```

#### 生产服务器
```bash
# 创建 .env 文件
cat > /opt/curios-api-prod/.env << EOF
ASPNETCORE_ENVIRONMENT=Production
POSTGRES_PASSWORD=your_secure_password_here
EOF
```

## 🔒 安全配置

### 1. SSH 密钥配置

```bash
# 在本地生成密钥对
ssh-keygen -t rsa -b 4096 -f ~/.ssh/curios_dev_key
ssh-keygen -t rsa -b 4096 -f ~/.ssh/curios_prod_key

# 将公钥复制到服务器
ssh-copy-id -i ~/.ssh/curios_dev_key.pub <EMAIL>
ssh-copy-id -i ~/.ssh/curios_prod_key.pub <EMAIL>
```

### 2. 防火墙配置

```bash
# 配置 UFW 防火墙
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# 查看状态
sudo ufw status
```

### 3. 域名配置

#### DNS 记录设置
```
# 开发环境
dev.yourdomain.com  A  <dev_server_ip>

# 生产环境
yourdomain.com      A  <prod_server_ip>
api.yourdomain.com  A  <prod_server_ip>
```

## 🚀 首次部署测试

### 开发服务器测试
```bash
# SSH 到开发服务器
ssh -i ~/.ssh/curios_dev_key <EMAIL>

# 进入项目目录
cd /opt/curios-api-dev

# 登录 GitHub Container Registry
echo $GITHUB_TOKEN | docker login ghcr.io -u your_username --password-stdin

# 启动服务
docker-compose up -d

# 检查服务状态
docker-compose ps
docker-compose logs -f

# 测试 API
curl http://localhost/weatherforecast
```

### 生产服务器测试
```bash
# SSH 到生产服务器
ssh -i ~/.ssh/curios_prod_key <EMAIL>

# 进入项目目录
cd /opt/curios-api-prod

# 登录 GitHub Container Registry
echo $GITHUB_TOKEN | docker login ghcr.io -u your_username --password-stdin

# 启动服务
docker-compose up -d

# 检查服务状态
docker-compose ps
docker-compose logs -f

# 测试 API
curl http://localhost/weatherforecast
```

## 📊 监控和维护

### 1. 系统监控
```bash
# 查看系统资源
htop

# 查看 Docker 容器资源使用
docker stats

# 查看磁盘使用
df -h

# 查看服务日志
docker-compose logs -f [service_name]
```

### 2. 定期维护
```bash
# 清理未使用的 Docker 镜像
docker system prune -f

# 备份数据库
docker exec curios-postgres-dev pg_dump -U orleans orleansdb > backup_$(date +%Y%m%d).sql

# 更新系统
sudo apt update && sudo apt upgrade -y
```

### 3. 故障排查
```bash
# 重启服务
docker-compose restart

# 查看容器状态
docker-compose ps

# 查看特定服务日志
docker-compose logs -f api

# 进入容器调试
docker exec -it curios-api-dev bash
```

## 🎯 GitHub Secrets 配置

在 GitHub 仓库的 Settings > Secrets and variables > Actions 中添加：

### 开发环境
- `DEV_HOST`: 开发服务器IP或域名
- `DEV_USERNAME`: SSH 用户名
- `DEV_SSH_KEY`: 开发服务器私钥内容

### 生产环境
- `PROD_HOST`: 生产服务器IP或域名
- `PROD_USERNAME`: SSH 用户名
- `PROD_SSH_KEY`: 生产服务器私钥内容

## ✅ 部署验证清单

### 开发环境
- [ ] 服务器基础环境配置完成
- [ ] Docker 和 Docker Compose 安装成功
- [ ] 项目目录创建并配置正确
- [ ] SSH 密钥配置完成
- [ ] GitHub Secrets 配置完成
- [ ] 首次手动部署成功
- [ ] API 接口测试通过
- [ ] 域名解析正确

### 生产环境
- [ ] 服务器基础环境配置完成
- [ ] Docker 和 Docker Compose 安装成功
- [ ] 项目目录创建并配置正确
- [ ] SSH 密钥配置完成
- [ ] 安全配置（防火墙、密码等）完成
- [ ] GitHub Secrets 配置完成
- [ ] 首次手动部署成功
- [ ] API 接口测试通过
- [ ] 域名解析正确
- [ ] SSL 证书配置（可选）

完成以上配置后，你的 CI/CD 流水线就可以正常工作了！
