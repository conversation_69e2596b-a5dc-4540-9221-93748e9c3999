# 🔄 替换 Aspire 完整指南

## 📊 当前组件分析

### **基础设施组件（保留）**
- ✅ **PostgreSQL** - Orleans 持久化存储
- ✅ **Redis** - Orleans 集群管理  
- ✅ **Nginx** - 负载均衡

### **Aspire 功能替换方案**
- 🔧 **服务编排** → Docker Compose + 启动脚本
- 📊 **日志聚合** → Serilog + Seq
- 📈 **分布式追踪** → OpenTelemetry + Jaeger
- 📋 **监控面板** → Grafana + Prometheus
- 🔍 **服务发现** → 环境变量配置

## 🛠️ 替换步骤

### **步骤1：移除 Aspire 项目**

```bash
# 删除 Aspire 相关项目
rm -rf src/Curios.AppHost
rm -rf src/Curios.ServiceDefaults

# 更新解决方案文件
# 从 Curios.sln 中移除这两个项目的引用
```

### **步骤2：更新项目引用**

**API 项目 (src/Curios.Api/Curios.ApiService.csproj):**
```xml
<!-- 移除 -->
<ProjectReference Include="..\Curios.ServiceDefaults\Curios.ServiceDefaults.csproj" />

<!-- 添加 -->
<ProjectReference Include="..\Curios.Shared\Curios.Shared.csproj" />
```

**Silo 项目 (src/Curios.Silo/Curios.Silo.csproj):**
```xml
<!-- 移除 -->
<ProjectReference Include="..\Curios.ServiceDefaults\Curios.ServiceDefaults.csproj" />

<!-- 添加 -->
<ProjectReference Include="..\Curios.Shared\Curios.Shared.csproj" />
```

### **步骤3：更新 Program.cs 文件**

**API Program.cs:**
```csharp
// 替换
// builder.AddServiceDefaults();
// 为
builder.AddStandardServices();

// 替换
// app.MapDefaultEndpoints();
// 为
app.MapStandardEndpoints();
```

**Silo Program.cs:**
```csharp
// 替换
// builder.AddServiceDefaults();
// 为
builder.AddStandardServices();
```

### **步骤4：启动监控服务**

```bash
# 启动基础设施 + 监控
cd deploy/docker
docker-compose -f docker-compose.dev.yml -f docker-compose.monitoring.yml up -d

# 访问监控面板
# - Seq 日志: http://localhost:5341
# - Jaeger 追踪: http://localhost:16686
# - Grafana 监控: http://localhost:3000 (admin/admin)
# - Prometheus: http://localhost:9090
```

### **步骤5：配置应用程序**

**appsettings.json 添加配置:**
```json
{
  "Logging": {
    "Seq": {
      "ServerUrl": "http://localhost:5341"
    }
  },
  "OpenTelemetry": {
    "Jaeger": {
      "Endpoint": "http://localhost:14268/api/traces"
    }
  }
}
```

## 🚀 新的开发流程

### **本地开发**
```bash
# 1. 启动基础设施和监控
./deploy/scripts/start-monitoring.sh

# 2. 启动应用服务
dotnet run --project src/Curios.Silo
dotnet run --project src/Curios.Api

# 3. 访问监控面板
# - 应用日志: http://localhost:5341
# - 分布式追踪: http://localhost:16686
# - 系统监控: http://localhost:3000
```

### **生产部署**
```bash
# 部署完整环境（包含监控）
docker-compose -f docker-compose.yml -f docker-compose.monitoring.yml up -d

# 或者分别部署
docker-compose up -d                    # 应用服务
docker-compose -f docker-compose.monitoring.yml up -d  # 监控服务
```

## 📊 监控面板访问

### **日志管理 - Seq**
- **地址**: http://localhost:5341
- **功能**: 结构化日志查询、实时日志流、日志分析
- **特点**: 专为 .NET 优化，支持结构化查询

### **分布式追踪 - Jaeger**
- **地址**: http://localhost:16686
- **功能**: 请求链路追踪、性能分析、依赖关系图
- **特点**: 轻量级，易于部署

### **系统监控 - Grafana**
- **地址**: http://localhost:3000 (admin/admin)
- **功能**: 系统指标、自定义仪表板、告警
- **特点**: 丰富的可视化选项

### **指标收集 - Prometheus**
- **地址**: http://localhost:9090
- **功能**: 指标查询、告警规则配置
- **特点**: 强大的查询语言 PromQL

## 🔧 配置说明

### **日志配置**
- **控制台输出**: 开发时实时查看
- **文件输出**: logs/app-{date}.log
- **Seq 输出**: 结构化日志存储和查询
- **日志级别**: Information（可在 appsettings 中调整）

### **追踪配置**
- **自动追踪**: HTTP 请求、数据库查询
- **自定义追踪**: 可添加业务逻辑追踪
- **采样率**: 默认 100%（生产环境建议调整）

### **指标配置**
- **系统指标**: CPU、内存、网络
- **应用指标**: HTTP 请求数、响应时间
- **业务指标**: 可自定义添加

## 📈 性能对比

### **Aspire 版本**
- ✅ 开发体验好
- ✅ 一键启动
- ❌ 生产环境复杂
- ❌ 依赖较重

### **替换后版本**
- ✅ 生产就绪
- ✅ 组件独立
- ✅ 更好的可观测性
- ❌ 配置稍复杂

## 🎯 推荐使用场景

### **选择替换方案的情况**
- 🎯 主要在服务器环境开发
- 🎯 需要生产级监控
- 🎯 团队熟悉传统监控工具
- 🎯 希望减少依赖复杂度

### **继续使用 Aspire 的情况**
- 🎯 主要在本地开发
- 🎯 团队刚接触分布式系统
- 🎯 快速原型开发
- 🎯 Microsoft 技术栈深度集成
