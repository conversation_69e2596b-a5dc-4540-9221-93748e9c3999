# ⚡ 快速开始指南

## 🎯 5分钟快速体验

### **第一步：启动基础设施**
```bash
# 启动 PostgreSQL + Redis + 监控组件
./scripts/infrastructure.sh local monitoring
```

等待输出显示：
```
✅ 本地开发环境启动完成！

基础服务：
  🐘 PostgreSQL:       localhost:5432 (orleans/orleans123)
  🔴 Redis:            localhost:6379

监控服务：
  📊 Seq 日志:         http://localhost:5341
  🔍 Jaeger 追踪:      http://localhost:16686
  📈 Grafana 监控:     http://localhost:3000 (admin/admin)
  📊 Prometheus:       http://localhost:9090
```

### **第二步：启动应用程序**
```bash
# 启动 Orleans Silo + API 服务
./scripts/run-app.sh start local
```

等待输出显示：
```
✅ 应用程序启动完成！

访问地址：
  🌐 API:              http://localhost:5314
  📖 Swagger:         http://localhost:5314/swagger
  ❤️  健康检查:        http://localhost:5314/health
```

### **第三步：测试 API**
```bash
# 测试健康检查
curl http://localhost:5314/health

# 测试示例 API
curl http://localhost:5314/weatherforecast

# 访问 Swagger UI
open http://localhost:5314/swagger
```

## 🔧 常用操作

### **查看状态**
```bash
# 查看基础设施状态
./scripts/infrastructure.sh status local

# 查看应用程序状态
./scripts/run-app.sh status
```

### **查看日志**
```bash
# 通过 Seq 查看结构化日志
open http://localhost:5341

# 查看基础设施日志
./scripts/infrastructure.sh logs local postgres
./scripts/infrastructure.sh logs local redis
```

### **停止服务**
```bash
# 停止应用程序
./scripts/run-app.sh stop

# 停止基础设施
./scripts/infrastructure.sh stop local
```

## 🚀 开发工作流

### **日常开发流程**
```bash
# 1. 启动开发环境
./scripts/infrastructure.sh local monitoring
./scripts/run-app.sh start local

# 2. 开发和测试
# - 修改代码
# - 通过 Swagger 测试 API
# - 通过 Seq 查看日志
# - 通过 Jaeger 查看追踪

# 3. 停止环境
./scripts/run-app.sh stop
./scripts/infrastructure.sh stop local
```

### **调试流程**
```bash
# 1. 启动基础设施
./scripts/infrastructure.sh local monitoring

# 2. 在 IDE 中调试启动应用程序
# - 在 Visual Studio/VS Code 中设置断点
# - F5 启动调试

# 3. 通过监控工具查看
# - Seq: http://localhost:5341 (日志)
# - Jaeger: http://localhost:16686 (追踪)
# - Grafana: http://localhost:3000 (监控)
```

## 🌐 环境部署

### **开发环境（完整容器化）**
```bash
# 启动开发环境
./scripts/infrastructure.sh development

# 访问
open http://localhost
open http://localhost:9000  # Portainer 管理界面
```

### **生产环境（高可用）**
```bash
# 启动生产环境
./scripts/infrastructure.sh production

# 访问
open http://localhost
open http://localhost:9000  # Portainer 管理界面
```

## 📊 监控面板使用

### **Seq 日志管理**
- **访问**: http://localhost:5341
- **功能**: 结构化日志查询、实时日志流
- **使用**: 
  - 搜索特定日志：`@Level = 'Error'`
  - 按时间过滤：选择时间范围
  - 按服务过滤：`Application = 'Curios.Api'`

### **Jaeger 分布式追踪**
- **访问**: http://localhost:16686
- **功能**: 请求链路追踪、性能分析
- **使用**:
  - 选择服务：Curios.Api 或 Curios.Silo
  - 查看请求链路和耗时
  - 分析性能瓶颈

### **Grafana 监控面板**
- **访问**: http://localhost:3000 (admin/admin)
- **功能**: 系统指标、自定义仪表板
- **使用**:
  - 查看系统资源使用情况
  - 监控应用程序指标
  - 设置告警规则

## 🚨 常见问题

### **端口冲突**
```bash
# 检查端口占用
lsof -i :5432  # PostgreSQL
lsof -i :6379  # Redis
lsof -i :5314  # API

# 解决方案：停止冲突服务
./scripts/infrastructure.sh stop local
```

### **数据库连接失败**
```bash
# 检查 PostgreSQL 状态
./scripts/infrastructure.sh status local

# 查看 PostgreSQL 日志
./scripts/infrastructure.sh logs local postgres

# 重启 PostgreSQL
./scripts/infrastructure.sh stop local
./scripts/infrastructure.sh local
```

### **Orleans 集群问题**
```bash
# 检查 Redis 状态
./scripts/infrastructure.sh logs local redis

# 重启应用程序
./scripts/run-app.sh restart local
```

### **应用程序启动失败**
```bash
# 检查应用程序状态
./scripts/run-app.sh status

# 查看应用程序日志
open http://localhost:5341

# 重启应用程序
./scripts/run-app.sh restart local
```

## 📈 性能优化建议

### **本地开发优化**
- 仅启动必要的监控组件
- 使用 SSD 存储 Docker 卷
- 调整 Docker Desktop 资源分配

### **开发效率提升**
- 使用 Swagger UI 测试 API
- 通过 Seq 实时查看日志
- 利用 Jaeger 分析请求性能
- 设置 IDE 断点调试

## 🔗 相关链接

- [完整文档](README.md)
- [基础设施指南](INFRASTRUCTURE_GUIDE.md)
- [开发环境指南](DEVELOPMENT_GUIDE.md)
- [Aspire 使用指南](ASPIRE_USAGE.md)
