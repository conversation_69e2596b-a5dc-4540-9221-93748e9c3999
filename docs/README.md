# 📚 Curios API 文档中心

## 📋 文档索引

### ⚡ **快速开始**

- **[快速开始指南](QUICK_START.md)** - 5 分钟快速体验完整系统

### 🏗️ **基础设施与部署**

- **[基础设施组件分离指南](INFRASTRUCTURE_GUIDE.md)** - 完整的组件分离架构和使用方法
- **[开发环境指南](DEVELOPMENT_GUIDE.md)** - 开发环境搭建和使用流程

### 🔄 **Aspire 相关**

- **[Aspire 使用指南](ASPIRE_USAGE.md)** - Aspire 在不同环境中的使用方法
- **[替换 Aspire 指南](REPLACE_ASPIRE_GUIDE.md)** - 如何用其他组件替换 Aspire
- **[移除 Aspire 指南](REMOVE_ASPIRE_GUIDE.md)** - 完全移除 Aspire 的步骤

## 🚀 快速开始

### **本地开发**

```bash
# 1. 启动基础设施
./scripts/infrastructure.sh local monitoring

# 2. 启动应用程序
./scripts/run-app.sh start local

# 3. 访问应用
# API: http://localhost:5314
# Swagger: http://localhost:5314/swagger
```

### **开发环境部署**

```bash
# 启动完整开发环境
./scripts/infrastructure.sh development

# 访问: http://localhost
```

### **生产环境部署**

```bash
# 启动生产环境
./scripts/infrastructure.sh production

# 访问: http://localhost
```

## 📊 架构概览

### **组件架构**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Curios API    │    │  Curios Silo    │    │   基础设施组件   │
│                 │    │                 │    │                 │
│ • REST API      │    │ • Orleans Grain │    │ • PostgreSQL    │
│ • Swagger UI    │    │ • 业务逻辑      │    │ • Redis         │
│ • 健康检查      │    │ • 数据持久化    │    │ • 监控组件      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   监控与日志     │
                    │                 │
                    │ • Seq 日志      │
                    │ • Jaeger 追踪   │
                    │ • Grafana 监控  │
                    │ • Prometheus    │
                    └─────────────────┘
```

### **环境配置**

| 环境            | 用途     | 配置文件                              | 访问方式                |
| --------------- | -------- | ------------------------------------- | ----------------------- |
| **Local**       | 本地开发 | `config/environments/local.env`       | 本地运行 + 容器基础设施 |
| **Development** | 开发测试 | `config/environments/development.env` | 完整容器化部署          |
| **Production**  | 生产环境 | `config/environments/production.env`  | 高可用容器化部署        |

## 🛠️ 管理脚本

### **基础设施管理**

```bash
./scripts/infrastructure.sh <command> [options]

# 常用命令
local [monitoring] [tools]  # 启动本地环境
development                 # 启动开发环境
production                  # 启动生产环境
status [env]               # 查看状态
logs <env> [service]       # 查看日志
stop <env>                 # 停止环境
cleanup                    # 清理资源
```

### **应用程序管理**

```bash
./scripts/run-app.sh <command> [environment]

# 常用命令
start <env>     # 启动应用程序
stop            # 停止应用程序
restart <env>   # 重启应用程序
silo <env>      # 仅启动 Silo
api <env>       # 仅启动 API
status          # 查看状态
```

## 📈 监控与日志

### **监控组件**

| 组件           | 用途           | 本地访问地址           | 默认凭据    |
| -------------- | -------------- | ---------------------- | ----------- |
| **Seq**        | 结构化日志查询 | http://localhost:5341  | -           |
| **Jaeger**     | 分布式追踪     | http://localhost:16686 | -           |
| **Grafana**    | 监控面板       | http://localhost:3000  | admin/admin |
| **Prometheus** | 指标收集       | http://localhost:9090  | -           |

### **管理工具**

| 工具                | 用途            | 本地访问地址          | 默认凭据                  |
| ------------------- | --------------- | --------------------- | ------------------------- |
| **PgAdmin**         | PostgreSQL 管理 | http://localhost:8080 | <EMAIL>/admin123 |
| **Redis Commander** | Redis 管理      | http://localhost:8081 | -                         |
| **Portainer**       | Docker 管理     | http://localhost:9000 | admin/admin               |

## 🔧 配置说明

### **连接字符串**

```json
{
  "ConnectionStrings": {
    "redis": "localhost:6379",
    "orleansdb": "Host=localhost;Database=orleansdb;Username=orleans;Password=**********"
  }
}
```

### **Orleans 配置**

```json
{
  "Orleans": {
    "ClusterId": "curios-local-cluster",
    "ServiceId": "curios-local-service"
  }
}
```

### **监控配置**

```json
{
  "Logging": {
    "Seq": {
      "ServerUrl": "http://localhost:5341"
    }
  },
  "OpenTelemetry": {
    "Jaeger": {
      "Endpoint": "http://localhost:14268/api/traces"
    }
  }
}
```

## 🚨 故障排除

### **常见问题**

1. **端口冲突** - 检查端口占用：`lsof -i :5432`
2. **数据库连接失败** - 检查 PostgreSQL 状态：`./scripts/infrastructure.sh status local`
3. **Orleans 集群问题** - 重启应用程序：`./scripts/run-app.sh restart local`

### **日志查看**

```bash
# 基础设施日志
./scripts/infrastructure.sh logs local postgres

# 应用程序日志（通过 Seq）
# 访问 http://localhost:5341
```

## 📞 支持

如有问题，请查看相关文档或检查日志输出。每个组件都有详细的状态检查和日志记录功能。
