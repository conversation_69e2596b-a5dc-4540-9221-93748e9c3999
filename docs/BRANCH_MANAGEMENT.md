# 🌳 分支管理策略和 CI/CD 工作流

## 📋 分支类型和用途

### 🎯 主要分支

| 分支      | 用途      | 环境        | 保护规则          |
| --------- | --------- | ----------- | ----------------- |
| `main`    | 生产环境  | Production  | ✅ 需要 PR + 审核 |
| `develop` | 开发/测试 | Development | ✅ 需要 PR        |

### 🔧 辅助分支

| 分支类型    | 命名规范              | 从哪创建  | 合并到哪           |
| ----------- | --------------------- | --------- | ------------------ |
| `feature/*` | `feature/user-auth`   | `develop` | `develop`          |
| `release/*` | `release/v1.2.0`      | `develop` | `main` + `develop` |
| `hotfix/*`  | `hotfix/critical-bug` | `main`    | `main` + `develop` |

## 🚀 CI/CD 自动化流程

### 环境部署规则

```mermaid
graph LR
    A[Push to develop] --> B[Deploy to DEV]
    C[Push to test] --> D[Deploy to TEST]
    E[Push to main] --> F[Deploy to PROD]
```

### 所需的 GitHub Secrets

```bash
# 开发环境
DEV_HOST=dev.yourdomain.com
DEV_USERNAME=ubuntu
DEV_SSH_KEY=<private_key_content>

# 生产环境
PROD_HOST=prod.yourdomain.com
PROD_USERNAME=ubuntu
PROD_SSH_KEY=<private_key_content>
```

## 📝 开发工作流程

### 1. 功能开发流程

```bash
# 1. 从 develop 创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/user-authentication

# 2. 开发功能
# ... 编写代码 ...

# 3. 提交代码
git add .
git commit -m "feat: add user authentication"
git push origin feature/user-authentication

# 4. 创建 PR 到 develop
# 在 GitHub 上创建 Pull Request: feature/user-authentication -> develop

# 5. 代码审核通过后合并
# 合并后自动部署到开发环境
```

### 2. 测试发布流程

```bash
# 1. 从 develop 合并到 test
git checkout test
git pull origin test
git merge develop
git push origin test

# 2. 自动部署到测试环境
# CI/CD 自动触发部署和冒烟测试

# 3. QA 团队在测试环境验证功能
```

### 3. 生产发布流程

```bash
# 1. 创建发布分支
git checkout develop
git pull origin develop
git checkout -b release/v1.2.0

# 2. 发布准备（版本号、文档等）
# 更新版本号、CHANGELOG 等

# 3. 合并到 test 进行最终验证
git checkout test
git merge release/v1.2.0
git push origin test

# 4. 验证通过后合并到 main
git checkout main
git merge release/v1.2.0
git push origin main

# 5. 同步回 develop
git checkout develop
git merge release/v1.2.0
git push origin develop

# 6. 删除发布分支
git branch -d release/v1.2.0
git push origin --delete release/v1.2.0
```

### 4. 热修复流程

```bash
# 1. 从 main 创建热修复分支
git checkout main
git pull origin main
git checkout -b hotfix/critical-security-fix

# 2. 修复问题
# ... 修复代码 ...

# 3. 合并到 main（紧急发布）
git checkout main
git merge hotfix/critical-security-fix
git push origin main

# 4. 同步到 develop
git checkout develop
git merge hotfix/critical-security-fix
git push origin develop

# 5. 删除热修复分支
git branch -d hotfix/critical-security-fix
git push origin --delete hotfix/critical-security-fix
```

## 🛡️ 分支保护规则

### GitHub 分支保护设置

#### main 分支

- ✅ Require pull request reviews before merging
- ✅ Require status checks to pass before merging
- ✅ Require branches to be up to date before merging
- ✅ Include administrators
- ✅ Restrict pushes that create files larger than 100MB

#### develop 分支

- ✅ Require pull request reviews before merging
- ✅ Require status checks to pass before merging
- ✅ Require branches to be up to date before merging

#### test 分支

- ✅ Require status checks to pass before merging

## 📊 环境对比

| 环境            | 用途     | 数据     | 性能     | 监控     |
| --------------- | -------- | -------- | -------- | -------- |
| **Development** | 开发调试 | 测试数据 | 单实例   | 基础日志 |
| **Test**        | QA 测试  | 仿真数据 | 接近生产 | 完整监控 |
| **Production**  | 用户使用 | 真实数据 | 高可用   | 全面监控 |

## 🎯 最佳实践

### Commit 消息规范

```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式
refactor: 重构
test: 测试相关
chore: 构建/工具相关
```

### PR 标题规范

```
[Feature] 用户认证功能
[Fix] 修复登录问题
[Hotfix] 紧急修复安全漏洞
[Release] v1.2.0 发布
```

### 版本号规范

遵循 [Semantic Versioning](https://semver.org/)：

- `MAJOR.MINOR.PATCH`
- 例如：`1.2.3`

## 🚨 应急处理

### 回滚生产环境

```bash
# 1. 快速回滚到上一个版本
git checkout main
git revert HEAD
git push origin main

# 2. 或者回滚到指定版本
git reset --hard <previous_commit_hash>
git push origin main --force-with-lease
```

### 紧急停机维护

```bash
# SSH 到生产服务器
ssh -i ~/.ssh/prod_key <EMAIL>

# 停止服务
cd /opt/curios-api
docker-compose -f deploy/docker/docker-compose.yml down

# 维护完成后重启
docker-compose -f deploy/docker/docker-compose.yml up -d
```
