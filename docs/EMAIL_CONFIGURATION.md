# 邮件服务配置指南

## 概述

Curios 系统使用 SMTP 协议发送邮件，包括验证码邮件、欢迎邮件和密码重置邮件。

## 配置步骤

### 1. Gmail 配置（推荐）

如果你使用 Gmail 作为邮件服务提供商：

1. **启用两步验证**
   - 登录你的 Google 账户
   - 前往 [Google 账户安全设置](https://myaccount.google.com/security)
   - 启用两步验证

2. **生成应用专用密码**
   - 在安全设置中，找到"应用专用密码"
   - 选择"邮件"和你的设备
   - 生成一个 16 位的应用专用密码

3. **更新配置文件**
   
   在 `src/Curios.Silo/appsettings.json` 和 `src/Curios.Api/appsettings.Development.json` 中：
   
   ```json
   {
     "Email": {
       "Smtp": {
         "Host": "smtp.gmail.com",
         "Port": "587",
         "Username": "<EMAIL>",
         "Password": "your-16-digit-app-password"
       },
       "FromAddress": "<EMAIL>",
       "FromName": "Curios"
     }
   }
   ```

### 2. 其他邮件服务提供商

#### Outlook/Hotmail
```json
{
  "Email": {
    "Smtp": {
      "Host": "smtp-mail.outlook.com",
      "Port": "587",
      "Username": "<EMAIL>",
      "Password": "your-password"
    },
    "FromAddress": "<EMAIL>",
    "FromName": "Curios"
  }
}
```

#### QQ 邮箱
```json
{
  "Email": {
    "Smtp": {
      "Host": "smtp.qq.com",
      "Port": "587",
      "Username": "<EMAIL>",
      "Password": "your-authorization-code"
    },
    "FromAddress": "<EMAIL>",
    "FromName": "Curios"
  }
}
```

#### 163 邮箱
```json
{
  "Email": {
    "Smtp": {
      "Host": "smtp.163.com",
      "Port": "587",
      "Username": "<EMAIL>",
      "Password": "your-authorization-code"
    },
    "FromAddress": "<EMAIL>",
    "FromName": "Curios"
  }
}
```

### 3. 环境变量配置（推荐用于生产环境）

为了安全起见，建议在生产环境中使用环境变量：

```bash
export Email__Smtp__Host="smtp.gmail.com"
export Email__Smtp__Port="587"
export Email__Smtp__Username="<EMAIL>"
export Email__Smtp__Password="your-app-password"
export Email__FromAddress="<EMAIL>"
export Email__FromName="Curios"
```

或者在 Docker 中：

```yaml
environment:
  - Email__Smtp__Host=smtp.gmail.com
  - Email__Smtp__Port=587
  - Email__Smtp__Username=<EMAIL>
  - Email__Smtp__Password=your-app-password
  - Email__FromAddress=<EMAIL>
  - Email__FromName=Curios
```

## 测试邮件发送

1. **启动应用程序**
   ```bash
   dotnet run --project src/Curios.AppHost
   ```

2. **发送测试邮件**
   - 访问 Swagger UI: http://localhost:5000/swagger
   - 使用 `/api/auth/email/send-verification` 端点
   - 输入一个有效的邮箱地址

3. **检查日志**
   - 在开发环境中，如果配置不完整，邮件内容会记录在控制台中
   - 在生产环境中，会尝试实际发送邮件

## 故障排除

### 常见错误

1. **认证失败**
   - 检查用户名和密码是否正确
   - 对于 Gmail，确保使用应用专用密码而不是账户密码

2. **连接超时**
   - 检查 SMTP 服务器地址和端口
   - 确保网络连接正常

3. **SSL/TLS 错误**
   - 大多数现代邮件服务都需要 SSL/TLS
   - 代码中已默认启用 `EnableSsl = true`

### 调试模式

在开发环境中，如果 `ASPNETCORE_ENVIRONMENT` 设置为 `Development`，系统会：
- 不实际发送邮件
- 在控制台中记录邮件内容
- 返回发送成功的状态

这样可以在没有配置真实 SMTP 的情况下测试应用程序逻辑。

## 安全建议

1. **不要在代码中硬编码密码**
2. **使用应用专用密码而不是主密码**
3. **在生产环境中使用环境变量或密钥管理服务**
4. **定期轮换邮件服务密码**
5. **监控邮件发送日志以检测异常活动**
