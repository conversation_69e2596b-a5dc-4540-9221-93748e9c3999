# 🌐 与现有 Nginx 整合指南

## 🎯 问题分析

你的服务器已经有 Nginx 在运行，我们需要：
1. **禁用容器中的 Nginx** - 避免端口冲突
2. **配置现有 Nginx** - 添加反向代理规则
3. **调整端口映射** - 直接暴露应用端口

## 🔧 解决方案

### **方案1：修改 Docker 配置（推荐）**

#### **1. 修改开发环境配置**

创建无 Nginx 版本的配置：

```yaml
# deploy/docker/docker-compose.dev-no-nginx.yml
version: '3.8'

services:
  # 基础设施服务保持不变
  redis:
    image: redis:7-alpine
    container_name: curios-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  postgres:
    image: postgres:16-alpine
    container_name: curios-postgres-dev
    environment:
      POSTGRES_DB: orleansdb
      POSTGRES_USER: orleans
      POSTGRES_PASSWORD: orleans123
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./init-orleans-db.sql:/docker-entrypoint-initdb.d/init-orleans-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U orleans -d orleansdb"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Orleans Silo
  silo:
    image: ghcr.io/hxw/curios-api-silo:develop
    container_name: curios-silo-dev
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__redis=redis:6379
      - ConnectionStrings__orleansdb=Host=postgres;Database=orleansdb;Username=orleans;Password=orleans123
      - ORLEANS_SILO_NAME=silo-dev
      - ORLEANS_CLUSTER_ID=curios-dev-cluster
      - ORLEANS_SERVICE_ID=curios-dev-service
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
    ports:
      - "11111:8080"

  # API 服务 - 直接暴露端口
  api:
    image: ghcr.io/hxw/curios-api-api:develop
    container_name: curios-api-dev
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__redis=redis:6379
      - ORLEANS_CLUSTER_ID=curios-dev-cluster
      - ORLEANS_SERVICE_ID=curios-dev-service
    depends_on:
      - silo
    ports:
      - "5001:8080"  # 直接暴露API端口

  # 管理工具（可选）
  portainer:
    image: portainer/portainer-ce:latest
    container_name: curios-portainer-dev
    ports:
      - "9000:9000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - portainer_dev_data:/data
    restart: unless-stopped
    profiles:
      - tools

volumes:
  redis_dev_data:
  postgres_dev_data:
  portainer_dev_data:
```

#### **2. 配置现有 Nginx**

在你的 Nginx 配置中添加：

```nginx
# /etc/nginx/sites-available/curios-dev
server {
    listen 80;
    server_name dev.yourdomain.com;
    
    # API 反向代理
    location / {
        proxy_pass http://localhost:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket 支持（如果需要）
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 健康检查端点
    location /health {
        proxy_pass http://localhost:5001/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 管理工具代理（可选）
    location /portainer/ {
        proxy_pass http://localhost:9000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 开发环境标识
    add_header X-Environment "Development" always;
}

# 生产环境配置
server {
    listen 80;
    server_name yourdomain.com api.yourdomain.com;
    
    # 负载均衡到多个API实例
    location / {
        proxy_pass http://api_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 生产环境安全头
    add_header X-Environment "Production" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
}

# 负载均衡配置
upstream api_backend {
    server localhost:5002;  # API实例1
    server localhost:5003;  # API实例2
}
```

#### **3. 启用配置**

```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/curios-dev /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重载配置
sudo systemctl reload nginx
```

### **方案2：使用不同端口（简单方案）**

如果你不想修改现有 Nginx 配置，可以：

```yaml
# 在 docker-compose 中使用不同端口
services:
  nginx:
    ports:
      - "8080:80"  # 使用8080端口而不是80
```

然后通过 `http://your-server:8080` 访问。

## 🚀 部署脚本更新

创建适配现有 Nginx 的部署脚本：

```bash
#!/bin/bash
# deploy/scripts/start-dev-no-nginx.sh

echo "启动开发环境（无Nginx容器）..."

cd "$(dirname "$0")/../docker"

# 使用无Nginx版本的配置
docker-compose -f docker-compose.dev-no-nginx.yml up -d

echo "开发环境已启动！"
echo ""
echo "服务端口："
echo "- API: http://localhost:5001"
echo "- Silo Dashboard: http://localhost:11111"
echo "- Redis: localhost:6379"
echo "- PostgreSQL: localhost:5432"
echo ""
echo "请确保你的 Nginx 已配置反向代理到 localhost:5001"
echo "然后可以通过 http://dev.yourdomain.com 访问API"
```

## 🔧 CI/CD 配置调整

更新 GitHub Actions 以使用新的配置：

```yaml
# .github/workflows/ci-cd.yml
- name: Deploy to Development Environment
  uses: appleboy/ssh-action@v1.0.3
  with:
    host: ${{ secrets.DEV_HOST }}
    username: ${{ secrets.DEV_USERNAME }}
    key: ${{ secrets.DEV_SSH_KEY }}
    script: |
      cd /opt/curios-api-dev
      
      # 使用无Nginx版本
      docker-compose -f docker-compose.yml pull
      docker-compose -f docker-compose.yml up -d --remove-orphans
      
      # 重载Nginx配置
      sudo systemctl reload nginx
      
      # 清理旧镜像
      docker image prune -f
      
      # 健康检查
      sleep 30
      curl -f http://localhost:5001/weatherforecast || echo "Health check failed"
```

## 📊 配置对比

### **容器Nginx vs 现有Nginx**

| 方面 | 容器Nginx | 现有Nginx |
|------|-----------|-----------|
| **配置管理** | Docker配置文件 | 系统配置文件 |
| **端口使用** | 占用80端口 | 复用现有80端口 |
| **SSL证书** | 需要单独配置 | 可复用现有证书 |
| **其他服务** | 可能冲突 | 完美整合 |
| **维护性** | 容器化管理 | 系统级管理 |

## 🎯 推荐方案

基于你的情况，我推荐：

1. **使用方案1** - 禁用容器Nginx，配置现有Nginx
2. **保留init-orleans-db.sql** - PostgreSQL存储仍然需要
3. **调整端口映射** - 直接暴露应用端口
4. **更新部署脚本** - 适配新的配置

需要我帮你实施这个整合方案吗？
