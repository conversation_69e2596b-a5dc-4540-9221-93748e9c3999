using System.Security.Cryptography;
using System.Text;

namespace Curios.Shared.Utils;

/// <summary>
/// 密码哈希工具类
/// </summary>
public static class PasswordHasher
{
    private const int SaltSize = 32; // 256 bits
    private const int HashSize = 32; // 256 bits
    private const int Iterations = 10000; // PBKDF2 迭代次数

    /// <summary>
    /// 哈希密码
    /// </summary>
    /// <param name="password">原始密码</param>
    /// <returns>哈希结果（包含盐值）</returns>
    public static (string hash, string salt) HashPassword(string password)
    {
        if (string.IsNullOrEmpty(password))
            throw new ArgumentException("Password cannot be null or empty", nameof(password));

        // 生成随机盐值
        var salt = GenerateSalt();
        
        // 计算哈希
        var hash = ComputeHash(password, salt);
        
        return (Convert.ToBase64String(hash), Convert.ToBase64String(salt));
    }

    /// <summary>
    /// 验证密码
    /// </summary>
    /// <param name="password">原始密码</param>
    /// <param name="hash">存储的哈希值</param>
    /// <param name="salt">存储的盐值</param>
    /// <returns>是否匹配</returns>
    public static bool VerifyPassword(string password, string hash, string salt)
    {
        if (string.IsNullOrEmpty(password) || string.IsNullOrEmpty(hash) || string.IsNullOrEmpty(salt))
            return false;

        try
        {
            var saltBytes = Convert.FromBase64String(salt);
            var hashBytes = Convert.FromBase64String(hash);
            
            var computedHash = ComputeHash(password, saltBytes);
            
            return CryptographicOperations.FixedTimeEquals(hashBytes, computedHash);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 生成随机盐值
    /// </summary>
    /// <returns>盐值字节数组</returns>
    private static byte[] GenerateSalt()
    {
        var salt = new byte[SaltSize];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(salt);
        return salt;
    }

    /// <summary>
    /// 计算密码哈希
    /// </summary>
    /// <param name="password">密码</param>
    /// <param name="salt">盐值</param>
    /// <returns>哈希值</returns>
    private static byte[] ComputeHash(string password, byte[] salt)
    {
        using var pbkdf2 = new Rfc2898DeriveBytes(
            Encoding.UTF8.GetBytes(password),
            salt,
            Iterations,
            HashAlgorithmName.SHA256);
        
        return pbkdf2.GetBytes(HashSize);
    }

    /// <summary>
    /// 生成随机验证码
    /// </summary>
    /// <param name="length">验证码长度</param>
    /// <returns>数字验证码</returns>
    public static string GenerateVerificationCode(int length = 6)
    {
        if (length <= 0)
            throw new ArgumentException("Length must be greater than 0", nameof(length));

        var code = new StringBuilder();
        using var rng = RandomNumberGenerator.Create();
        
        for (int i = 0; i < length; i++)
        {
            var bytes = new byte[4];
            rng.GetBytes(bytes);
            var randomNumber = Math.Abs(BitConverter.ToInt32(bytes, 0)) % 10;
            code.Append(randomNumber);
        }
        
        return code.ToString();
    }

    /// <summary>
    /// 生成随机字符串
    /// </summary>
    /// <param name="length">字符串长度</param>
    /// <param name="includeSpecialChars">是否包含特殊字符</param>
    /// <returns>随机字符串</returns>
    public static string GenerateRandomString(int length, bool includeSpecialChars = false)
    {
        if (length <= 0)
            throw new ArgumentException("Length must be greater than 0", nameof(length));

        const string letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        const string numbers = "0123456789";
        const string specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?";
        
        var chars = letters + numbers;
        if (includeSpecialChars)
            chars += specialChars;

        var result = new StringBuilder();
        using var rng = RandomNumberGenerator.Create();
        
        for (int i = 0; i < length; i++)
        {
            var bytes = new byte[4];
            rng.GetBytes(bytes);
            var randomIndex = Math.Abs(BitConverter.ToInt32(bytes, 0)) % chars.Length;
            result.Append(chars[randomIndex]);
        }
        
        return result.ToString();
    }
}
