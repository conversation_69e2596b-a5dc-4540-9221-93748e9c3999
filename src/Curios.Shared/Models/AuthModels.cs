using System.ComponentModel.DataAnnotations;
using Orleans;

namespace Curios.Shared.Models;

/// <summary>
/// 邮箱注册请求
/// </summary>
public class EmailRegistrationRequest
{
    /// <summary>
    /// 邮箱地址
    /// </summary>
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
}

/// <summary>
/// 邮箱验证请求
/// </summary>
public class EmailVerificationRequest
{
    /// <summary>
    /// 邮箱地址
    /// </summary>
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// 验证码
    /// </summary>
    [Required]
    [StringLength(6, MinimumLength = 6)]
    public string VerificationCode { get; set; } = string.Empty;

    /// <summary>
    /// 密码
    /// </summary>
    [Required]
    [StringLength(100, MinimumLength = 8)]
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// 显示名称（可选）
    /// </summary>
    [StringLength(50)]
    public string? DisplayName { get; set; }
}

/// <summary>
/// 邮箱登录请求
/// </summary>
public class EmailLoginRequest
{
    /// <summary>
    /// 邮箱地址
    /// </summary>
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// 密码
    /// </summary>
    [Required]
    public string Password { get; set; } = string.Empty;
}

/// <summary>
/// Google 登录请求
/// </summary>
public class GoogleLoginRequest
{
    /// <summary>
    /// Google ID Token
    /// </summary>
    [Required]
    public string IdToken { get; set; } = string.Empty;
}

/// <summary>
/// Apple 登录请求
/// </summary>
public class AppleLoginRequest
{
    /// <summary>
    /// Apple Identity Token
    /// </summary>
    [Required]
    public string IdentityToken { get; set; } = string.Empty;

    /// <summary>
    /// Apple Authorization Code
    /// </summary>
    [Required]
    public string AuthorizationCode { get; set; } = string.Empty;

    /// <summary>
    /// 用户信息（首次登录时提供）
    /// </summary>
    public AppleUserInfo? User { get; set; }
}

/// <summary>
/// Apple 用户信息
/// </summary>
[GenerateSerializer]
public class AppleUserInfo
{
    /// <summary>
    /// 用户名
    /// </summary>
    [Id(0)]
    public AppleUserName? Name { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    [Id(1)]
    public string? Email { get; set; }
}

/// <summary>
/// Apple 用户名
/// </summary>
[GenerateSerializer]
public class AppleUserName
{
    /// <summary>
    /// 名字
    /// </summary>
    [Id(0)]
    public string? FirstName { get; set; }

    /// <summary>
    /// 姓氏
    /// </summary>
    [Id(1)]
    public string? LastName { get; set; }
}

/// <summary>
/// 认证响应
/// </summary>
[GenerateSerializer]
public class AuthenticationResponse
{
    /// <summary>
    /// 是否成功
    /// </summary>
    [Id(0)]
    public bool Success { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    [Id(1)]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 用户信息
    /// </summary>
    [Id(2)]
    public User? User { get; set; }

    /// <summary>
    /// JWT Token
    /// </summary>
    [Id(3)]
    public string? Token { get; set; }

    /// <summary>
    /// Token 过期时间
    /// </summary>
    [Id(4)]
    public DateTime? TokenExpiresAt { get; set; }

    /// <summary>
    /// 刷新 Token
    /// </summary>
    [Id(5)]
    public string? RefreshToken { get; set; }
}

/// <summary>
/// 邮箱验证响应
/// </summary>
[GenerateSerializer]
public class EmailVerificationResponse
{
    /// <summary>
    /// 是否成功
    /// </summary>
    [Id(0)]
    public bool Success { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    [Id(1)]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 验证码过期时间
    /// </summary>
    [Id(2)]
    public DateTime? ExpiresAt { get; set; }
}

/// <summary>
/// JWT 载荷
/// </summary>
[GenerateSerializer]
public class JwtPayload
{
    /// <summary>
    /// 用户ID
    /// </summary>
    [Id(0)]
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱
    /// </summary>
    [Id(1)]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// 显示名称
    /// </summary>
    [Id(2)]
    public string? DisplayName { get; set; }

    /// <summary>
    /// 签发时间
    /// </summary>
    [Id(3)]
    public long Iat { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    [Id(4)]
    public long Exp { get; set; }
}

/// <summary>
/// 第三方认证用户信息
/// </summary>
[GenerateSerializer]
public class ExternalUserInfo
{
    /// <summary>
    /// 提供商类型
    /// </summary>
    [Id(0)]
    public AuthProviderType ProviderType { get; set; }

    /// <summary>
    /// 提供商用户ID
    /// </summary>
    [Id(1)]
    public string ProviderId { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱
    /// </summary>
    [Id(2)]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// 显示名称
    /// </summary>
    [Id(3)]
    public string? DisplayName { get; set; }

    /// <summary>
    /// 头像URL
    /// </summary>
    [Id(4)]
    public string? AvatarUrl { get; set; }

    /// <summary>
    /// 邮箱是否已验证
    /// </summary>
    [Id(5)]
    public bool IsEmailVerified { get; set; }
}
