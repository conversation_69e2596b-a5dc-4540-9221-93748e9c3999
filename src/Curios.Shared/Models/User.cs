using System.ComponentModel.DataAnnotations;
using Orleans;

namespace Curios.Shared.Models;

/// <summary>
/// 用户实体
/// </summary>
[GenerateSerializer]
public class User
{
    /// <summary>
    /// 用户唯一标识
    /// </summary>
    [Id(0)]
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱地址
    /// </summary>
    [Required]
    [EmailAddress]
    [Id(1)]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// 用户显示名称
    /// </summary>
    [Id(2)]
    public string? DisplayName { get; set; }

    /// <summary>
    /// 头像URL
    /// </summary>
    [Id(3)]
    public string? AvatarUrl { get; set; }

    /// <summary>
    /// 账户状态
    /// </summary>
    [Id(4)]
    public UserStatus Status { get; set; } = UserStatus.Active;

    /// <summary>
    /// 邮箱是否已验证
    /// </summary>
    [Id(5)]
    public bool IsEmailVerified { get; set; }

    /// <summary>
    /// 认证提供商列表
    /// </summary>
    [Id(6)]
    public List<AuthenticationProvider> AuthProviders { get; set; } = new();

    /// <summary>
    /// 创建时间
    /// </summary>
    [Id(7)]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    [Id(8)]
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后登录时间
    /// </summary>
    [Id(9)]
    public DateTime? LastLoginAt { get; set; }
}

/// <summary>
/// 用户状态枚举
/// </summary>
public enum UserStatus
{
    /// <summary>
    /// 活跃状态
    /// </summary>
    Active = 1,

    /// <summary>
    /// 已停用
    /// </summary>
    Inactive = 2,

    /// <summary>
    /// 已删除
    /// </summary>
    Deleted = 3,

    /// <summary>
    /// 待验证邮箱
    /// </summary>
    PendingEmailVerification = 4
}

/// <summary>
/// 认证提供商
/// </summary>
[GenerateSerializer]
public class AuthenticationProvider
{
    /// <summary>
    /// 提供商类型
    /// </summary>
    [Id(0)]
    public AuthProviderType ProviderType { get; set; }

    /// <summary>
    /// 提供商用户ID
    /// </summary>
    [Id(1)]
    public string ProviderId { get; set; } = string.Empty;

    /// <summary>
    /// 提供商用户邮箱
    /// </summary>
    [Id(2)]
    public string? ProviderEmail { get; set; }

    /// <summary>
    /// 密码哈希（仅用于邮箱注册）
    /// </summary>
    [Id(3)]
    public string? PasswordHash { get; set; }

    /// <summary>
    /// 密码盐值（仅用于邮箱注册）
    /// </summary>
    [Id(4)]
    public string? PasswordSalt { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [Id(5)]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后使用时间
    /// </summary>
    [Id(6)]
    public DateTime LastUsedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 认证提供商类型
/// </summary>
public enum AuthProviderType
{
    /// <summary>
    /// 邮箱密码
    /// </summary>
    Email = 1,

    /// <summary>
    /// Google OAuth
    /// </summary>
    Google = 2,

    /// <summary>
    /// Apple Sign-In
    /// </summary>
    Apple = 3
}

/// <summary>
/// 邮箱验证记录
/// </summary>
[GenerateSerializer]
public class EmailVerification
{
    /// <summary>
    /// 邮箱地址
    /// </summary>
    [Id(0)]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// 验证码
    /// </summary>
    [Id(1)]
    public string VerificationCode { get; set; } = string.Empty;

    /// <summary>
    /// 验证码类型
    /// </summary>
    [Id(2)]
    public VerificationType Type { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    [Id(3)]
    public DateTime ExpiresAt { get; set; }

    /// <summary>
    /// 是否已使用
    /// </summary>
    [Id(4)]
    public bool IsUsed { get; set; }

    /// <summary>
    /// 尝试次数
    /// </summary>
    [Id(5)]
    public int AttemptCount { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [Id(6)]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 验证类型
/// </summary>
public enum VerificationType
{
    /// <summary>
    /// 邮箱注册验证
    /// </summary>
    EmailRegistration = 1,

    /// <summary>
    /// 密码重置
    /// </summary>
    PasswordReset = 2,

    /// <summary>
    /// 邮箱变更验证
    /// </summary>
    EmailChange = 3
}
