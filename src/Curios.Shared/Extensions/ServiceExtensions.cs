using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using OpenTelemetry;
using OpenTelemetry.Metrics;
using OpenTelemetry.Trace;
using OpenTelemetry.Resources;
using Serilog;
using Serilog.Events;
using Prometheus;

namespace Curios.Shared.Extensions;

public static class ServiceExtensions
{
    private const string HealthEndpointPath = "/health";
    private const string AlivenessEndpointPath = "/alive";
    private const string MetricsEndpointPath = "/metrics";

    /// <summary>
    /// 添加标准服务配置（替代 Aspire ServiceDefaults）
    /// </summary>
    public static TBuilder AddStandardServices<TBuilder>(this TBuilder builder) 
        where TBuilder : IHostApplicationBuilder
    {
        // 配置 Serilog 日志
        builder.AddSerilogLogging();
        
        // 配置 OpenTelemetry 追踪
        builder.AddOpenTelemetryTracing();
        
        // 配置 Prometheus 指标
        builder.AddPrometheusMetrics();
        
        // 添加健康检查
        builder.AddStandardHealthChecks();

        // 配置 HTTP 客户端
        builder.Services.ConfigureHttpClientDefaults(http =>
        {
            // 添加标准弹性处理
            http.AddStandardResilienceHandler();
        });

        return builder;
    }

    /// <summary>
    /// 配置 Serilog 日志
    /// </summary>
    public static TBuilder AddSerilogLogging<TBuilder>(this TBuilder builder) 
        where TBuilder : IHostApplicationBuilder
    {
        // 清除默认日志提供程序
        builder.Logging.ClearProviders();

        // 配置 Serilog
        var logger = new LoggerConfiguration()
            .MinimumLevel.Information()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
            .MinimumLevel.Override("Microsoft.AspNetCore", LogEventLevel.Warning)
            .MinimumLevel.Override("Orleans", LogEventLevel.Information)
            .Enrich.FromLogContext()
            .Enrich.WithProperty("Application", builder.Environment.ApplicationName)
            .Enrich.WithProperty("Environment", builder.Environment.EnvironmentName)
            .WriteTo.Console(outputTemplate: 
                "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}")
            .WriteTo.File("logs/app-.log", 
                rollingInterval: RollingInterval.Day,
                retainedFileCountLimit: 7,
                outputTemplate: 
                "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}");

        // 如果配置了 Seq 服务器，添加 Seq 输出
        var seqServerUrl = builder.Configuration["Logging:Seq:ServerUrl"];
        if (!string.IsNullOrEmpty(seqServerUrl))
        {
            logger.WriteTo.Seq(seqServerUrl);
        }

        Log.Logger = logger.CreateLogger();
        builder.Services.AddSerilog();

        return builder;
    }

    /// <summary>
    /// 配置 OpenTelemetry 分布式追踪
    /// </summary>
    public static TBuilder AddOpenTelemetryTracing<TBuilder>(this TBuilder builder) 
        where TBuilder : IHostApplicationBuilder
    {
        builder.Services.AddOpenTelemetry()
            .ConfigureResource(resource => resource
                .AddService(builder.Environment.ApplicationName)
                .AddAttributes(new Dictionary<string, object>
                {
                    ["service.version"] = "1.0.0",
                    ["deployment.environment"] = builder.Environment.EnvironmentName
                }))
            .WithTracing(tracing =>
            {
                tracing
                    .AddSource(builder.Environment.ApplicationName)
                    .AddAspNetCoreInstrumentation(options =>
                    {
                        // 排除健康检查和指标端点
                        options.Filter = context =>
                            !context.Request.Path.StartsWithSegments(HealthEndpointPath) &&
                            !context.Request.Path.StartsWithSegments(AlivenessEndpointPath) &&
                            !context.Request.Path.StartsWithSegments(MetricsEndpointPath);
                    })
                    .AddHttpClientInstrumentation()
                    .AddEntityFrameworkCoreInstrumentation();

                // 配置导出器
                var jaegerEndpoint = builder.Configuration["OpenTelemetry:Jaeger:Endpoint"];
                if (!string.IsNullOrEmpty(jaegerEndpoint))
                {
                    tracing.AddJaegerExporter(options =>
                    {
                        options.Endpoint = new Uri(jaegerEndpoint);
                    });
                }
                else
                {
                    // 默认使用控制台导出器用于开发
                    tracing.AddConsoleExporter();
                }
            });

        return builder;
    }

    /// <summary>
    /// 配置 Prometheus 指标
    /// </summary>
    public static TBuilder AddPrometheusMetrics<TBuilder>(this TBuilder builder) 
        where TBuilder : IHostApplicationBuilder
    {
        builder.Services.AddOpenTelemetry()
            .WithMetrics(metrics =>
            {
                metrics
                    .AddAspNetCoreInstrumentation()
                    .AddHttpClientInstrumentation()
                    .AddRuntimeInstrumentation()
                    .AddPrometheusExporter();
            });

        return builder;
    }

    /// <summary>
    /// 添加标准健康检查
    /// </summary>
    public static TBuilder AddStandardHealthChecks<TBuilder>(this TBuilder builder) 
        where TBuilder : IHostApplicationBuilder
    {
        builder.Services.AddHealthChecks()
            .AddCheck("self", () => HealthCheckResult.Healthy(), ["live"]);

        // 添加 Redis 健康检查
        var redisConnectionString = builder.Configuration["ConnectionStrings:redis"];
        if (!string.IsNullOrEmpty(redisConnectionString))
        {
            builder.Services.AddHealthChecks()
                .AddRedis(redisConnectionString, name: "redis", tags: ["ready"]);
        }

        // 添加 PostgreSQL 健康检查
        var postgresConnectionString = builder.Configuration["ConnectionStrings:orleansdb"];
        if (!string.IsNullOrEmpty(postgresConnectionString))
        {
            builder.Services.AddHealthChecks()
                .AddNpgSql(postgresConnectionString, name: "postgres", tags: ["ready"]);
        }

        return builder;
    }

    /// <summary>
    /// 映射标准端点
    /// </summary>
    public static WebApplication MapStandardEndpoints(this WebApplication app)
    {
        // 健康检查端点
        if (app.Environment.IsDevelopment())
        {
            app.MapHealthChecks(HealthEndpointPath);
            app.MapHealthChecks(AlivenessEndpointPath, new HealthCheckOptions
            {
                Predicate = r => r.Tags.Contains("live")
            });
        }

        // Prometheus 指标端点
        app.MapMetrics();

        return app;
    }
}
