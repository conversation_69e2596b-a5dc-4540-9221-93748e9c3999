﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

  <ItemGroup>
    <!-- Orleans 相关 -->
    <PackageReference Include="Microsoft.Orleans.Core.Abstractions" Version="9.2.1" />

    <!-- 日志相关 -->
    <PackageReference Include="Serilog" Version="4.2.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.Seq" Version="8.0.0" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />

    <!-- OpenTelemetry 追踪相关 -->
    <PackageReference Include="OpenTelemetry" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.EntityFrameworkCore" Version="1.0.0-beta.12" />
    <PackageReference Include="OpenTelemetry.Exporter.Jaeger" Version="1.5.1" />
    <PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.12.0" />

    <!-- Prometheus 指标相关 -->
    <PackageReference Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" Version="1.12.0-rc1" />
    <PackageReference Include="prometheus-net" Version="8.2.1" />
    <PackageReference Include="prometheus-net.AspNetCore" Version="8.2.1" />

    <!-- 健康检查相关 -->
    <PackageReference Include="AspNetCore.HealthChecks.Redis" Version="8.0.1" />
    <PackageReference Include="AspNetCore.HealthChecks.Npgsql" Version="8.0.2" />

    <!-- 弹性处理 -->
    <PackageReference Include="Microsoft.Extensions.Http.Resilience" Version="9.7.0" />
  </ItemGroup>

</Project>
