using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Npgsql;

namespace Curios.Silo;

public class OrleansTablesInitializer : IHostedService
{
    private readonly ILogger<OrleansTablesInitializer> _logger;
    private readonly IConfiguration _configuration;

    public OrleansTablesInitializer(ILogger<OrleansTablesInitializer> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        var connectionString = _configuration["ConnectionStrings:orleansdb"];
        if (string.IsNullOrEmpty(connectionString))
        {
            _logger.LogInformation("No PostgreSQL connection string found, skipping Orleans tables initialization");
            return;
        }

        try
        {
            _logger.LogInformation("Initializing Orleans tables...");

            // Wait for database to be ready
            await WaitForDatabaseAsync(connectionString, cancellationToken);

            // Initialize Orleans tables
            await InitializeOrleansTablesAsync(connectionString, cancellationToken);

            _logger.LogInformation("Orleans tables initialization completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize Orleans tables");
            // Don't throw - let Orleans handle missing tables gracefully
        }
    }

    public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;

    private async Task WaitForDatabaseAsync(string connectionString, CancellationToken cancellationToken)
    {
        var maxRetries = 30;
        var delay = TimeSpan.FromSeconds(2);

        for (int i = 0; i < maxRetries; i++)
        {
            try
            {
                using var connection = new NpgsqlConnection(connectionString);
                await connection.OpenAsync(cancellationToken);
                _logger.LogInformation("Database is ready");
                return;
            }
            catch (Exception) when (i < maxRetries - 1)
            {
                _logger.LogWarning("Database not ready, retrying in {Delay}s... ({Attempt}/{MaxRetries})",
                    delay.TotalSeconds, i + 1, maxRetries);
                await Task.Delay(delay, cancellationToken);
            }
        }

        throw new InvalidOperationException("Database is not available after maximum retries");
    }

    private async Task InitializeOrleansTablesAsync(string connectionString, CancellationToken cancellationToken)
    {
        using var connection = new NpgsqlConnection(connectionString);
        await connection.OpenAsync(cancellationToken);

        // Check if Orleans tables already exist
        var checkTablesSql = @"
            SELECT COUNT(*) FROM information_schema.tables
            WHERE table_name IN ('orleansstorage', 'orleansquery')";

        using var checkCommand = new NpgsqlCommand(checkTablesSql, connection);
        var existingTables = (long)(await checkCommand.ExecuteScalarAsync(cancellationToken) ?? 0L);

        if (existingTables >= 2)
        {
            _logger.LogInformation("Orleans tables already exist, skipping initialization");
            return;
        }

        _logger.LogInformation("Creating Orleans storage tables...");

        // Create Orleans storage tables using official schema
        var createTablesSql = @"
            -- Orleans Storage Table
            CREATE TABLE IF NOT EXISTS OrleansStorage
            (
                GrainIdHash integer NOT NULL,
                GrainIdN0 bigint NOT NULL,
                GrainIdN1 bigint NOT NULL,
                GrainTypeHash integer NOT NULL,
                GrainTypeString varchar(512) NOT NULL,
                GrainIdExtensionString varchar(512) NULL,
                ServiceId varchar(150) NOT NULL,
                PayloadBinary bytea NULL,
                PayloadXml xml NULL,
                PayloadJson text NULL,
                ModifiedOn timestamp(3) NOT NULL,
                Version integer NULL,

                CONSTRAINT PK_Storage PRIMARY KEY(GrainIdHash, GrainTypeHash, ServiceId)
            );

            -- Orleans Query Table (required for ADO.NET storage)
            CREATE TABLE IF NOT EXISTS OrleansQuery
            (
                QueryKey varchar(64) NOT NULL,
                QueryText varchar(8000) NOT NULL,

                CONSTRAINT OrleansQuery_Key PRIMARY KEY(QueryKey)
            );

            -- Insert default queries (using lowercase column names to match PostgreSQL)
            INSERT INTO OrleansQuery(QueryKey, QueryText) VALUES
            ('ReadFromStorageKey','SELECT payloadbinary, payloadxml, payloadjson, modifiedon, version FROM orleansstorage WHERE grainidhash = @GrainIdHash AND graintypehash = @GrainTypeHash AND serviceid = @ServiceId AND grainidn0 = @GrainIdN0 AND grainidn1 = @GrainIdN1 AND grainidextensionstring = @GrainIdExtensionString')
            ON CONFLICT (QueryKey) DO NOTHING;

            INSERT INTO OrleansQuery(QueryKey, QueryText) VALUES
            ('WriteToStorageKey','INSERT INTO orleansstorage(grainidhash, grainidn0, grainidn1, graintypehash, graintypestring, grainidextensionstring, serviceid, payloadbinary, payloadxml, payloadjson, modifiedon, version) VALUES(@GrainIdHash, @GrainIdN0, @GrainIdN1, @GrainTypeHash, @GrainTypeString, @GrainIdExtensionString, @ServiceId, @PayloadBinary, @PayloadXml::xml, @PayloadJson, @ModifiedOn, @Version) ON CONFLICT (grainidhash, graintypehash, serviceid) DO UPDATE SET payloadbinary = @PayloadBinary, payloadxml = @PayloadXml::xml, payloadjson = @PayloadJson, modifiedon = @ModifiedOn, version = @Version')
            ON CONFLICT (QueryKey) DO NOTHING;

            INSERT INTO OrleansQuery(QueryKey, QueryText) VALUES
            ('ClearStorageKey','DELETE FROM orleansstorage WHERE grainidhash = @GrainIdHash AND graintypehash = @GrainTypeHash AND serviceid = @ServiceId AND grainidn0 = @GrainIdN0 AND grainidn1 = @GrainIdN1 AND grainidextensionstring = @GrainIdExtensionString AND version = @Version')
            ON CONFLICT (QueryKey) DO NOTHING;";

        using var command = new NpgsqlCommand(createTablesSql, connection);
        await command.ExecuteNonQueryAsync(cancellationToken);

        _logger.LogInformation("Orleans storage tables created successfully");
    }
}
