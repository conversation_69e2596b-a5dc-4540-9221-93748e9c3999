﻿using Orleans;
using Orleans.Hosting;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Curios.Grains;
using Curios.Silo;

var builder = Host.CreateApplicationBuilder(args);

// Add service defaults & Aspire components.
builder.AddServiceDefaults();

// Add Orleans tables initializer
builder.Services.AddHostedService<OrleansTablesInitializer>();

// Add HttpClient for external auth services
builder.Services.AddHttpClient();

// Add email service
builder.Services.AddScoped<Curios.Grains.Services.IEmailService, Curios.Grains.Services.EmailService>();

// Configure Orleans
builder.UseOrleans(siloBuilder =>
{
    // Always use Redis clustering for distributed simulation
    var redisConnectionString = builder.Configuration["ConnectionStrings:redis"] ?? "localhost:6379";
    siloBuilder.UseRedisClustering(redisConnectionString);

    // Configure cluster identity
    siloBuilder.Configure<Orleans.Configuration.ClusterOptions>(options =>
    {
        options.ClusterId = "curios-cluster";
        options.ServiceId = "curios-service";
    });

    // Configure grain storage - prefer PostgreSQL if available
    var postgresConnectionString = builder.Configuration["ConnectionStrings:orleansdb"];
    if (!string.IsNullOrEmpty(postgresConnectionString))
    {
        siloBuilder.AddAdoNetGrainStorage("userStore", options =>
        {
            options.Invariant = "Npgsql";
            options.ConnectionString = postgresConnectionString;
        });
    }
    else
    {
        // Fallback to memory storage for development
        siloBuilder.AddMemoryGrainStorage("userStore");
    }
});

var host = builder.Build();

await host.RunAsync();
