{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Orleans": "Information"}, "Seq": {"ServerUrl": ""}}, "OpenTelemetry": {"Jaeger": {"Endpoint": ""}}, "ConnectionStrings": {"redis": "localhost:6379", "orleansdb": "Host=localhost;Database=orleansdb;Username=orleans;Password=**********"}, "Orleans": {"ClusterId": "curios-local-cluster", "ServiceId": "curios-local-service"}, "Email": {"Smtp": {"Host": "smtp.qq.com", "Port": "587", "Username": "<EMAIL>", "Password": "5224"}, "FromAddress": "<EMAIL>", "FromName": "<PERSON><PERSON><PERSON>"}}