﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.8" />
    <PackageReference Include="Microsoft.Orleans.Clustering.AdoNet" Version="9.2.1" />
    <PackageReference Include="Microsoft.Orleans.Clustering.Redis" Version="9.2.1" />
    <PackageReference Include="Microsoft.Orleans.Persistence.AdoNet" Version="9.2.1" />
    <PackageReference Include="Microsoft.Orleans.Server" Version="9.2.1" />
    <PackageReference Include="Npgsql" Version="9.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Curios.Grains\Curios.Grains.csproj" />
    <ProjectReference Include="..\Curios.ServiceDefaults\Curios.ServiceDefaults.csproj" />
  </ItemGroup>

</Project>
