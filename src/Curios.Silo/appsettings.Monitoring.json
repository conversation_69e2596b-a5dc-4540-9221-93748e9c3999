{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Orleans": "Information"}, "Seq": {"ServerUrl": "http://localhost:5341"}}, "OpenTelemetry": {"Jaeger": {"Endpoint": "http://localhost:14268/api/traces"}}, "ConnectionStrings": {"redis": "localhost:6379", "orleansdb": "Host=localhost;Database=orleansdb;Username=orleans;Password=**********"}, "Orleans": {"ClusterId": "curios-cluster", "ServiceId": "curios-service"}, "Email": {"Smtp": {"Host": "smtp.qq.com", "Port": "587", "Username": "<EMAIL>", "Password": "5224"}, "FromAddress": "<EMAIL>", "FromName": "<PERSON><PERSON><PERSON>"}}