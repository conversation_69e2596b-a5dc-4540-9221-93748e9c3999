# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
USER app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["src/Curios.Silo/Curios.Silo.csproj", "src/Curios.Silo/"]
COPY ["src/Curios.Grains/Curios.Grains.csproj", "src/Curios.Grains/"]
COPY ["src/Curios.Grains.Interfaces/Curios.Grains.Interfaces.csproj", "src/Curios.Grains.Interfaces/"]
COPY ["src/Curios.ServiceDefaults/Curios.ServiceDefaults.csproj", "src/Curios.ServiceDefaults/"]
RUN dotnet restore "./src/Curios.Silo/Curios.Silo.csproj"
COPY . .
WORKDIR "/src/src/Curios.Silo"
RUN dotnet build "./Curios.Silo.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./Curios.Silo.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Curios.Silo.dll"]
