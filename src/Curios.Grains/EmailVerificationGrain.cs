using Orleans;
using Orleans.Runtime;
using Curios.Grains.Interfaces;
using Curios.Grains.States;
using Curios.Shared.Models;
using Curios.Shared.Utils;
using Curios.Grains.Services;
using Microsoft.Extensions.Logging;

namespace Curios.Grains;

/// <summary>
/// 邮箱验证 Grain 实现
/// </summary>
public class EmailVerificationGrain : Grain, IEmailVerificationGrain
{
    private readonly IPersistentState<EmailVerificationState> _state;
    private readonly IEmailService _emailService;
    private readonly ILogger<EmailVerificationGrain> _logger;

    public EmailVerificationGrain(
        [PersistentState("emailVerification", "userStore")] IPersistentState<EmailVerificationState> state,
        IEmailService emailService,
        ILogger<EmailVerificationGrain> logger)
    {
        _state = state;
        _emailService = emailService;
        _logger = logger;
    }

    public async Task<EmailVerificationResponse> SendVerificationCodeAsync(string email, VerificationType type)
    {
        try
        {
            // 检查是否有未过期的验证码
            if (_state.State.Verifications.TryGetValue(email, out var existingVerification))
            {
                if (!existingVerification.IsUsed && existingVerification.ExpiresAt > DateTime.UtcNow)
                {
                    // 如果距离上次发送不到1分钟，则拒绝重复发送
                    if (DateTime.UtcNow - existingVerification.CreatedAt < TimeSpan.FromMinutes(1))
                    {
                        return new EmailVerificationResponse
                        {
                            Success = false,
                            ErrorMessage = "请等待1分钟后再重新发送验证码",
                            ExpiresAt = existingVerification.ExpiresAt
                        };
                    }
                }
            }

            // 生成新的验证码
            var verificationCode = PasswordHasher.GenerateVerificationCode(6);
            var expiresAt = DateTime.UtcNow.AddMinutes(10); // 10分钟过期

            var verification = new EmailVerification
            {
                Email = email,
                VerificationCode = verificationCode,
                Type = type,
                ExpiresAt = expiresAt,
                IsUsed = false,
                AttemptCount = 0,
                CreatedAt = DateTime.UtcNow
            };

            // 保存验证记录
            _state.State.Verifications[email] = verification;
            await _state.WriteStateAsync();

            // 发送邮件
            var emailSent = await _emailService.SendVerificationCodeAsync(email, verificationCode, type.ToString());

            if (!emailSent)
            {
                _logger.LogWarning("Failed to send verification email to {Email}", email);
                // 即使邮件发送失败，也返回成功，避免暴露系统内部错误
                // 在生产环境中可能需要更复杂的错误处理策略
            }

            _logger.LogInformation("Verification code generated for {Email}, expires at {ExpiresAt}",
                email, expiresAt);

            return new EmailVerificationResponse
            {
                Success = true,
                ExpiresAt = expiresAt
            };
        }
        catch (Exception ex)
        {
            return new EmailVerificationResponse
            {
                Success = false,
                ErrorMessage = $"发送验证码失败: {ex.Message}"
            };
        }
    }

    public async Task<bool> VerifyCodeAsync(string email, string code)
    {
        if (!_state.State.Verifications.TryGetValue(email, out var verification))
        {
            return false;
        }

        // 检查验证码是否已使用
        if (verification.IsUsed)
        {
            return false;
        }

        // 检查验证码是否过期
        if (verification.ExpiresAt <= DateTime.UtcNow)
        {
            return false;
        }

        // 增加尝试次数
        verification.AttemptCount++;

        // 检查尝试次数是否超限（最多5次）
        if (verification.AttemptCount > 5)
        {
            verification.IsUsed = true;
            await _state.WriteStateAsync();
            return false;
        }

        // 验证验证码
        if (verification.VerificationCode != code)
        {
            await _state.WriteStateAsync();
            return false;
        }

        // 验证成功，标记为已使用
        verification.IsUsed = true;
        await _state.WriteStateAsync();

        return true;
    }

    public async Task<AuthenticationResponse> CompleteEmailRegistrationAsync(string email, string code, string password, string? displayName = null)
    {
        try
        {
            // 验证验证码
            if (!await VerifyCodeAsync(email, code))
            {
                return new AuthenticationResponse
                {
                    Success = false,
                    ErrorMessage = "验证码无效或已过期"
                };
            }

            // 检查用户是否已存在
            var authGrain = GrainFactory.GetGrain<IAuthenticationGrain>("auth");
            var existingUser = await authGrain.FindUserByEmailAsync(email);
            if (existingUser != null)
            {
                return new AuthenticationResponse
                {
                    Success = false,
                    ErrorMessage = "该邮箱已被注册"
                };
            }

            // 生成用户ID
            var userId = Guid.NewGuid().ToString();

            // 创建用户
            var userGrain = GrainFactory.GetGrain<IUserGrain>(userId);
            var user = await userGrain.CreateUserAsync(email, displayName);

            // 创建邮箱认证提供商
            var (passwordHash, passwordSalt) = PasswordHasher.HashPassword(password);
            var authProvider = new AuthenticationProvider
            {
                ProviderType = AuthProviderType.Email,
                ProviderId = email,
                ProviderEmail = email,
                PasswordHash = passwordHash,
                PasswordSalt = passwordSalt,
                CreatedAt = DateTime.UtcNow,
                LastUsedAt = DateTime.UtcNow
            };

            // 添加认证提供商
            await userGrain.AddAuthProviderAsync(authProvider);

            // 验证邮箱
            await userGrain.VerifyEmailAsync();

            // 更新最后登录时间
            await userGrain.UpdateLastLoginAsync();

            // 生成 JWT Token
            var token = await authGrain.GenerateJwtTokenAsync(user);

            // 发送欢迎邮件
            _ = Task.Run(async () =>
            {
                try
                {
                    await _emailService.SendWelcomeEmailAsync(email, displayName ?? "用户");
                    _logger.LogInformation("Welcome email sent to {Email}", email);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to send welcome email to {Email}", email);
                }
            });

            return new AuthenticationResponse
            {
                Success = true,
                User = user,
                Token = token,
                TokenExpiresAt = DateTime.UtcNow.AddHours(24) // 24小时过期
            };
        }
        catch (Exception ex)
        {
            return new AuthenticationResponse
            {
                Success = false,
                ErrorMessage = $"注册失败: {ex.Message}"
            };
        }
    }

    public Task<EmailVerification?> GetVerificationAsync(string email)
    {
        _state.State.Verifications.TryGetValue(email, out var verification);
        return Task.FromResult(verification);
    }

    public async Task CleanupExpiredCodesAsync()
    {
        var now = DateTime.UtcNow;
        var expiredEmails = _state.State.Verifications
            .Where(kvp => kvp.Value.ExpiresAt <= now || kvp.Value.IsUsed)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var email in expiredEmails)
        {
            _state.State.Verifications.Remove(email);
        }

        if (expiredEmails.Any())
        {
            _state.State.LastCleanupAt = now;
            await _state.WriteStateAsync();
        }
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        // 激活时清理过期验证码
        await CleanupExpiredCodesAsync();
        await base.OnActivateAsync(cancellationToken);
    }
}
