using Orleans;
using Curios.Shared.Models;

namespace Curios.Grains.States;

/// <summary>
/// 用户 Grain 状态
/// </summary>
[GenerateSerializer]
public class UserState
{
    /// <summary>
    /// 用户信息
    /// </summary>
    [Id(0)]
    public User? User { get; set; }

    /// <summary>
    /// 是否已初始化
    /// </summary>
    [Id(1)]
    public bool IsInitialized { get; set; }
}

/// <summary>
/// 邮箱验证 Grain 状态
/// </summary>
[GenerateSerializer]
public class EmailVerificationState
{
    /// <summary>
    /// 验证记录列表（按邮箱分组）
    /// </summary>
    [Id(0)]
    public Dictionary<string, EmailVerification> Verifications { get; set; } = new();

    /// <summary>
    /// 最后清理时间
    /// </summary>
    [Id(1)]
    public DateTime LastCleanupAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 认证配置状态
/// </summary>
[GenerateSerializer]
public class AuthConfigState
{
    /// <summary>
    /// JWT 密钥
    /// </summary>
    [Id(0)]
    public string JwtSecret { get; set; } = string.Empty;

    /// <summary>
    /// JWT 过期时间（小时）
    /// </summary>
    [Id(1)]
    public int JwtExpirationHours { get; set; } = 24;

    /// <summary>
    /// 刷新 Token 过期时间（天）
    /// </summary>
    [Id(2)]
    public int RefreshTokenExpirationDays { get; set; } = 30;

    /// <summary>
    /// Google OAuth 配置
    /// </summary>
    [Id(3)]
    public GoogleOAuthConfig GoogleConfig { get; set; } = new();

    /// <summary>
    /// Apple Sign-In 配置
    /// </summary>
    [Id(4)]
    public AppleSignInConfig AppleConfig { get; set; } = new();

    /// <summary>
    /// 邮件服务配置
    /// </summary>
    [Id(5)]
    public EmailServiceConfig EmailConfig { get; set; } = new();
}

/// <summary>
/// Google OAuth 配置
/// </summary>
[GenerateSerializer]
public class GoogleOAuthConfig
{
    /// <summary>
    /// 客户端 ID
    /// </summary>
    [Id(0)]
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 客户端密钥
    /// </summary>
    [Id(1)]
    public string ClientSecret { get; set; } = string.Empty;

    /// <summary>
    /// 重定向 URI
    /// </summary>
    [Id(2)]
    public string RedirectUri { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用
    /// </summary>
    [Id(3)]
    public bool IsEnabled { get; set; }
}

/// <summary>
/// Apple Sign-In 配置
/// </summary>
[GenerateSerializer]
public class AppleSignInConfig
{
    /// <summary>
    /// 客户端 ID
    /// </summary>
    [Id(0)]
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// Team ID
    /// </summary>
    [Id(1)]
    public string TeamId { get; set; } = string.Empty;

    /// <summary>
    /// Key ID
    /// </summary>
    [Id(2)]
    public string KeyId { get; set; } = string.Empty;

    /// <summary>
    /// 私钥内容
    /// </summary>
    [Id(3)]
    public string PrivateKey { get; set; } = string.Empty;

    /// <summary>
    /// 重定向 URI
    /// </summary>
    [Id(4)]
    public string RedirectUri { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用
    /// </summary>
    [Id(5)]
    public bool IsEnabled { get; set; }
}

/// <summary>
/// 邮件服务配置
/// </summary>
[GenerateSerializer]
public class EmailServiceConfig
{
    /// <summary>
    /// SMTP 服务器
    /// </summary>
    [Id(0)]
    public string SmtpServer { get; set; } = string.Empty;

    /// <summary>
    /// SMTP 端口
    /// </summary>
    [Id(1)]
    public int SmtpPort { get; set; } = 587;

    /// <summary>
    /// 用户名
    /// </summary>
    [Id(2)]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 密码
    /// </summary>
    [Id(3)]
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// 发件人邮箱
    /// </summary>
    [Id(4)]
    public string FromEmail { get; set; } = string.Empty;

    /// <summary>
    /// 发件人名称
    /// </summary>
    [Id(5)]
    public string FromName { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用 SSL
    /// </summary>
    [Id(6)]
    public bool EnableSsl { get; set; } = true;

    /// <summary>
    /// 是否启用
    /// </summary>
    [Id(7)]
    public bool IsEnabled { get; set; }
}
