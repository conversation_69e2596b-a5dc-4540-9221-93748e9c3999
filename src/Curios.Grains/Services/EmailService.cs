using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Net.Mail;
using System.Text;

namespace Curios.Grains.Services;

/// <summary>
/// 邮件服务实现
/// </summary>
public class EmailService : IEmailService
{
    private readonly ILogger<EmailService> _logger;
    private readonly IConfiguration _configuration;

    public EmailService(ILogger<EmailService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }

    public async Task<bool> SendVerificationCodeAsync(string toEmail, string verificationCode, string purpose)
    {
        try
        {
            var subject = GetVerificationSubject(purpose);
            var body = GetVerificationEmailBody(verificationCode, purpose);

            return await SendEmailAsync(toEmail, subject, body);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send verification code email to {Email}", toEmail);
            return false;
        }
    }

    public async Task<bool> SendWelcomeEmailAsync(string toEmail, string displayName)
    {
        try
        {
            var subject = "欢迎加入 Curios！";
            var body = GetWelcomeEmailBody(displayName);

            return await SendEmailAsync(toEmail, subject, body);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send welcome email to {Email}", toEmail);
            return false;
        }
    }

    public async Task<bool> SendPasswordResetEmailAsync(string toEmail, string resetToken)
    {
        try
        {
            var subject = "重置您的密码 - Curios";
            var body = GetPasswordResetEmailBody(resetToken);

            return await SendEmailAsync(toEmail, subject, body);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send password reset email to {Email}", toEmail);
            return false;
        }
    }

    private async Task<bool> SendEmailAsync(string toEmail, string subject, string body)
    {
        try
        {
            // 检查是否在开发环境
            var environment = _configuration["ASPNETCORE_ENVIRONMENT"];
            if (environment == "Development")
            {
                // 在开发环境中，只记录邮件内容而不实际发送
                _logger.LogInformation("📧 [DEV MODE] Email would be sent to: {Email}", toEmail);
                _logger.LogInformation("📧 [DEV MODE] Subject: {Subject}", subject);
                _logger.LogInformation("📧 [DEV MODE] Body: {Body}", body);
                return true;
            }

            // 获取 SMTP 配置
            var smtpHost = _configuration["Email:Smtp:Host"];
            var smtpPort = int.Parse(_configuration["Email:Smtp:Port"] ?? "587");
            var smtpUsername = _configuration["Email:Smtp:Username"];
            var smtpPassword = _configuration["Email:Smtp:Password"];
            var fromEmail = _configuration["Email:FromAddress"];
            var fromName = _configuration["Email:FromName"] ?? "Curios";

            if (string.IsNullOrEmpty(smtpHost) || string.IsNullOrEmpty(smtpUsername) || 
                string.IsNullOrEmpty(smtpPassword) || string.IsNullOrEmpty(fromEmail))
            {
                _logger.LogWarning("SMTP configuration is incomplete. Email not sent.");
                return false;
            }

            using var client = new SmtpClient(smtpHost, smtpPort);
            client.EnableSsl = true;
            client.Credentials = new NetworkCredential(smtpUsername, smtpPassword);

            using var message = new MailMessage();
            message.From = new MailAddress(fromEmail, fromName);
            message.To.Add(toEmail);
            message.Subject = subject;
            message.Body = body;
            message.IsBodyHtml = true;
            message.BodyEncoding = Encoding.UTF8;
            message.SubjectEncoding = Encoding.UTF8;

            await client.SendMailAsync(message);
            
            _logger.LogInformation("Email sent successfully to {Email}", toEmail);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email to {Email}", toEmail);
            return false;
        }
    }

    private static string GetVerificationSubject(string purpose)
    {
        return purpose switch
        {
            "EmailRegistration" => "验证您的邮箱 - Curios",
            "PasswordReset" => "重置密码验证码 - Curios",
            "EmailChange" => "验证新邮箱 - Curios",
            _ => "验证码 - Curios"
        };
    }

    private static string GetVerificationEmailBody(string verificationCode, string purpose)
    {
        var purposeText = purpose switch
        {
            "EmailRegistration" => "完成注册",
            "PasswordReset" => "重置密码",
            "EmailChange" => "更改邮箱",
            _ => "验证身份"
        };

        return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>验证码</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #4CAF50; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 20px; background-color: #f9f9f9; }}
        .code {{ font-size: 24px; font-weight: bold; color: #4CAF50; text-align: center; 
                 padding: 15px; background-color: #e8f5e8; border-radius: 5px; margin: 20px 0; }}
        .footer {{ padding: 20px; text-align: center; color: #666; font-size: 12px; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>Curios 验证码</h1>
        </div>
        <div class='content'>
            <h2>您好！</h2>
            <p>您正在{purposeText}，请使用以下验证码：</p>
            <div class='code'>{verificationCode}</div>
            <p>此验证码将在 10 分钟后过期。</p>
            <p>如果您没有请求此验证码，请忽略此邮件。</p>
        </div>
        <div class='footer'>
            <p>此邮件由 Curios 系统自动发送，请勿回复。</p>
        </div>
    </div>
</body>
</html>";
    }

    private static string GetWelcomeEmailBody(string displayName)
    {
        return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>欢迎加入 Curios</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #4CAF50; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 20px; background-color: #f9f9f9; }}
        .footer {{ padding: 20px; text-align: center; color: #666; font-size: 12px; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>欢迎加入 Curios！</h1>
        </div>
        <div class='content'>
            <h2>您好，{displayName}！</h2>
            <p>欢迎加入 Curios 社区！您的账户已成功创建。</p>
            <p>现在您可以开始探索我们的平台，享受各种功能和服务。</p>
            <p>如果您有任何问题或需要帮助，请随时联系我们的客服团队。</p>
            <p>再次欢迎您的加入！</p>
        </div>
        <div class='footer'>
            <p>此邮件由 Curios 系统自动发送，请勿回复。</p>
        </div>
    </div>
</body>
</html>";
    }

    private static string GetPasswordResetEmailBody(string resetToken)
    {
        return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>重置密码</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #FF9800; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 20px; background-color: #f9f9f9; }}
        .token {{ font-size: 16px; font-weight: bold; color: #FF9800; text-align: center; 
                  padding: 15px; background-color: #fff3e0; border-radius: 5px; margin: 20px 0; }}
        .footer {{ padding: 20px; text-align: center; color: #666; font-size: 12px; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>重置密码</h1>
        </div>
        <div class='content'>
            <h2>密码重置请求</h2>
            <p>我们收到了您的密码重置请求。请使用以下重置令牌：</p>
            <div class='token'>{resetToken}</div>
            <p>此重置令牌将在 30 分钟后过期。</p>
            <p>如果您没有请求重置密码，请忽略此邮件并确保您的账户安全。</p>
        </div>
        <div class='footer'>
            <p>此邮件由 Curios 系统自动发送，请勿回复。</p>
        </div>
    </div>
</body>
</html>";
    }
}
