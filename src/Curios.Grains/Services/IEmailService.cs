namespace Curios.Grains.Services;

/// <summary>
/// 邮件服务接口
/// </summary>
public interface IEmailService
{
    /// <summary>
    /// 发送验证码邮件
    /// </summary>
    /// <param name="toEmail">收件人邮箱</param>
    /// <param name="verificationCode">验证码</param>
    /// <param name="purpose">验证目的</param>
    /// <returns>是否发送成功</returns>
    Task<bool> SendVerificationCodeAsync(string toEmail, string verificationCode, string purpose);

    /// <summary>
    /// 发送欢迎邮件
    /// </summary>
    /// <param name="toEmail">收件人邮箱</param>
    /// <param name="displayName">用户显示名称</param>
    /// <returns>是否发送成功</returns>
    Task<bool> SendWelcomeEmailAsync(string toEmail, string displayName);

    /// <summary>
    /// 发送密码重置邮件
    /// </summary>
    /// <param name="toEmail">收件人邮箱</param>
    /// <param name="resetToken">重置令牌</param>
    /// <returns>是否发送成功</returns>
    Task<bool> SendPasswordResetEmailAsync(string toEmail, string resetToken);
}
