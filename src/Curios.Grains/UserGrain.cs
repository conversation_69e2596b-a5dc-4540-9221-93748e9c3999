using Orleans;
using Orleans.Runtime;
using Curios.Grains.Interfaces;
using Curios.Grains.States;
using Curios.Shared.Models;

namespace Curios.Grains;

/// <summary>
/// 用户 Grain 实现
/// </summary>
public class UserGrain : Grain, IUserGrain
{
    private readonly IPersistentState<UserState> _userState;

    public UserGrain([PersistentState("userState", "userStore")] IPersistentState<UserState> userState)
    {
        _userState = userState;
    }

    public Task<User?> GetUserAsync()
    {
        return Task.FromResult(_userState.State.User);
    }

    public async Task<User> CreateUserAsync(string email, string? displayName = null, string? avatarUrl = null)
    {
        if (_userState.State.IsInitialized)
            throw new InvalidOperationException("User already exists");

        var userId = this.GetPrimaryKeyString();
        var user = new User
        {
            UserId = userId,
            Email = email,
            DisplayName = displayName,
            AvatarUrl = avatarUrl,
            Status = UserStatus.Active,
            IsEmailVerified = false,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        _userState.State.User = user;
        _userState.State.IsInitialized = true;

        await _userState.WriteStateAsync();

        // 创建邮箱索引
        var emailIndexGrain = GrainFactory.GetGrain<IEmailIndexGrain>(email);
        await emailIndexGrain.SetUserIdAsync(userId);

        return user;
    }

    public async Task<User> UpdateUserAsync(string? displayName = null, string? avatarUrl = null)
    {
        if (!_userState.State.IsInitialized || _userState.State.User == null)
            throw new InvalidOperationException("User does not exist");

        var user = _userState.State.User;

        if (displayName != null)
            user.DisplayName = displayName;

        if (avatarUrl != null)
            user.AvatarUrl = avatarUrl;

        user.UpdatedAt = DateTime.UtcNow;

        await _userState.WriteStateAsync();
        return user;
    }

    public async Task<bool> AddAuthProviderAsync(AuthenticationProvider provider)
    {
        if (!_userState.State.IsInitialized || _userState.State.User == null)
            return false;

        var user = _userState.State.User;

        // 移除现有的同类型提供商
        user.AuthProviders.RemoveAll(p => p.ProviderType == provider.ProviderType);

        // 添加新的提供商
        user.AuthProviders.Add(provider);
        user.UpdatedAt = DateTime.UtcNow;

        await _userState.WriteStateAsync();

        // 创建提供商索引
        var providerKey = $"{provider.ProviderType}:{provider.ProviderId}";
        var providerIndexGrain = GrainFactory.GetGrain<IProviderIndexGrain>(providerKey);
        await providerIndexGrain.SetUserIdAsync(user.UserId);

        return true;
    }

    public async Task<bool> RemoveAuthProviderAsync(AuthProviderType providerType)
    {
        if (!_userState.State.IsInitialized || _userState.State.User == null)
            return false;

        var user = _userState.State.User;
        var removed = user.AuthProviders.RemoveAll(p => p.ProviderType == providerType) > 0;

        if (removed)
        {
            user.UpdatedAt = DateTime.UtcNow;
            await _userState.WriteStateAsync();
        }

        return removed;
    }

    public Task<AuthenticationProvider?> GetAuthProviderAsync(AuthProviderType providerType)
    {
        if (!_userState.State.IsInitialized || _userState.State.User == null)
            return Task.FromResult<AuthenticationProvider?>(null);

        var provider = _userState.State.User.AuthProviders
            .FirstOrDefault(p => p.ProviderType == providerType);

        return Task.FromResult(provider);
    }

    public async Task<bool> VerifyEmailAsync()
    {
        if (!_userState.State.IsInitialized || _userState.State.User == null)
            return false;

        var user = _userState.State.User;
        user.IsEmailVerified = true;
        user.UpdatedAt = DateTime.UtcNow;

        // 如果用户状态是待验证邮箱，则改为活跃状态
        if (user.Status == UserStatus.PendingEmailVerification)
        {
            user.Status = UserStatus.Active;
        }

        await _userState.WriteStateAsync();
        return true;
    }

    public async Task UpdateLastLoginAsync()
    {
        if (!_userState.State.IsInitialized || _userState.State.User == null)
            return;

        var user = _userState.State.User;
        user.LastLoginAt = DateTime.UtcNow;
        user.UpdatedAt = DateTime.UtcNow;

        await _userState.WriteStateAsync();
    }

    public async Task<bool> DeactivateUserAsync()
    {
        if (!_userState.State.IsInitialized || _userState.State.User == null)
            return false;

        var user = _userState.State.User;
        user.Status = UserStatus.Inactive;
        user.UpdatedAt = DateTime.UtcNow;

        await _userState.WriteStateAsync();
        return true;
    }

    public async Task<bool> ActivateUserAsync()
    {
        if (!_userState.State.IsInitialized || _userState.State.User == null)
            return false;

        var user = _userState.State.User;
        user.Status = UserStatus.Active;
        user.UpdatedAt = DateTime.UtcNow;

        await _userState.WriteStateAsync();
        return true;
    }

    public async Task<bool> DeleteUserAsync()
    {
        if (!_userState.State.IsInitialized || _userState.State.User == null)
            return false;

        var user = _userState.State.User;
        user.Status = UserStatus.Deleted;
        user.UpdatedAt = DateTime.UtcNow;

        await _userState.WriteStateAsync();
        return true;
    }

    public Task<bool> ExistsAsync()
    {
        return Task.FromResult(_userState.State.IsInitialized && _userState.State.User != null);
    }
}