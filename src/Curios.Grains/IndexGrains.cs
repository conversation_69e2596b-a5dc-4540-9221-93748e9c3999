using Orleans;
using Orleans.Runtime;

namespace Curios.Grains;

/// <summary>
/// 邮箱到用户ID的索引 Grain
/// </summary>
public class EmailIndexGrain : Grain, IEmailIndexGrain
{
    private readonly IPersistentState<EmailIndexState> _state;

    public EmailIndexGrain([PersistentState("emailIndex", "userStore")] IPersistentState<EmailIndexState> state)
    {
        _state = state;
    }

    public Task<string?> GetUserIdAsync()
    {
        return Task.FromResult(_state.State.UserId);
    }

    public async Task SetUserIdAsync(string userId)
    {
        _state.State.UserId = userId;
        _state.State.CreatedAt = DateTime.UtcNow;
        await _state.WriteStateAsync();
    }

    public async Task RemoveAsync()
    {
        _state.State.UserId = null;
        await _state.WriteStateAsync();
    }
}

/// <summary>
/// 认证提供商到用户ID的索引 Grain
/// </summary>
public class ProviderIndexGrain : Grain, IProviderIndexGrain
{
    private readonly IPersistentState<ProviderIndexState> _state;

    public ProviderIndexGrain([PersistentState("providerIndex", "userStore")] IPersistentState<ProviderIndexState> state)
    {
        _state = state;
    }

    public Task<string?> GetUserIdAsync()
    {
        return Task.FromResult(_state.State.UserId);
    }

    public async Task SetUserIdAsync(string userId)
    {
        _state.State.UserId = userId;
        _state.State.CreatedAt = DateTime.UtcNow;
        await _state.WriteStateAsync();
    }

    public async Task RemoveAsync()
    {
        _state.State.UserId = null;
        await _state.WriteStateAsync();
    }
}

/// <summary>
/// 邮箱索引状态
/// </summary>
[GenerateSerializer]
public class EmailIndexState
{
    /// <summary>
    /// 用户ID
    /// </summary>
    [Id(0)]
    public string? UserId { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [Id(1)]
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// 认证提供商索引状态
/// </summary>
[GenerateSerializer]
public class ProviderIndexState
{
    /// <summary>
    /// 用户ID
    /// </summary>
    [Id(0)]
    public string? UserId { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [Id(1)]
    public DateTime CreatedAt { get; set; }
}
