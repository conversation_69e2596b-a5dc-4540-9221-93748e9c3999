using Orleans;
using Orleans.Runtime;
using Curios.Grains.Interfaces;
using Curios.Shared.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Net.Http;
using System.Security.Cryptography;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

namespace Curios.Grains;

/// <summary>
/// 第三方认证 Grain 实现
/// </summary>
public class ExternalAuthGrain : Grain, IExternalAuthGrain
{
    private readonly ILogger<ExternalAuthGrain> _logger;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;

    public ExternalAuthGrain(
        ILogger<ExternalAuthGrain> logger,
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration)
    {
        _logger = logger;
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
    }

    public async Task<ExternalUserInfo?> ValidateGoogleTokenAsync(string idToken)
    {
        try
        {
            _logger.LogInformation("Validating Google ID token");

            // Google Token 验证端点
            var googleTokenInfoUrl = "https://oauth2.googleapis.com/tokeninfo";
            var httpClient = _httpClientFactory.CreateClient();

            var response = await httpClient.GetAsync($"{googleTokenInfoUrl}?id_token={idToken}");

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Google token validation failed with status: {StatusCode}", response.StatusCode);
                return null;
            }

            var content = await response.Content.ReadAsStringAsync();
            var tokenInfo = JsonSerializer.Deserialize<GoogleTokenInfo>(content);

            if (tokenInfo == null)
            {
                _logger.LogWarning("Failed to deserialize Google token info");
                return null;
            }

            // 验证 audience (client_id)
            var expectedClientId = _configuration["Authentication:Google:ClientId"];
            if (string.IsNullOrEmpty(expectedClientId) || tokenInfo.Aud != expectedClientId)
            {
                _logger.LogWarning("Google token audience validation failed. Expected: {Expected}, Actual: {Actual}",
                    expectedClientId, tokenInfo.Aud);
                return null;
            }

            // 验证 issuer
            if (tokenInfo.Iss != "accounts.google.com" && tokenInfo.Iss != "https://accounts.google.com")
            {
                _logger.LogWarning("Google token issuer validation failed: {Issuer}", tokenInfo.Iss);
                return null;
            }

            // 验证过期时间
            var expirationTime = DateTimeOffset.FromUnixTimeSeconds(tokenInfo.Exp);
            if (expirationTime < DateTimeOffset.UtcNow)
            {
                _logger.LogWarning("Google token has expired");
                return null;
            }

            _logger.LogInformation("Google token validated successfully for user: {Email}", tokenInfo.Email);

            return new ExternalUserInfo
            {
                ProviderType = AuthProviderType.Google,
                ProviderId = tokenInfo.Sub,
                Email = tokenInfo.Email,
                DisplayName = tokenInfo.Name,
                AvatarUrl = tokenInfo.Picture,
                IsEmailVerified = tokenInfo.EmailVerified
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating Google token");
            return null;
        }
    }

    public Task<ExternalUserInfo?> ValidateAppleTokenAsync(string identityToken, string authorizationCode)
    {
        try
        {
            _logger.LogInformation("Validating Apple identity token");

            // 解析 JWT Token（不验证签名，因为我们需要从 Apple 获取公钥）
            var handler = new JwtSecurityTokenHandler();
            var jsonToken = handler.ReadJwtToken(identityToken);

            // 验证基本信息
            var issuer = jsonToken.Claims.FirstOrDefault(x => x.Type == "iss")?.Value;
            var audience = jsonToken.Claims.FirstOrDefault(x => x.Type == "aud")?.Value;
            var subject = jsonToken.Claims.FirstOrDefault(x => x.Type == "sub")?.Value;
            var email = jsonToken.Claims.FirstOrDefault(x => x.Type == "email")?.Value;
            var emailVerified = jsonToken.Claims.FirstOrDefault(x => x.Type == "email_verified")?.Value;

            // 验证 issuer
            if (issuer != "https://appleid.apple.com")
            {
                _logger.LogWarning("Apple token issuer validation failed: {Issuer}", issuer);
                return Task.FromResult<ExternalUserInfo?>(null);
            }

            // 验证 audience (client_id)
            var expectedClientId = _configuration["Authentication:Apple:ClientId"];
            if (string.IsNullOrEmpty(expectedClientId) || audience != expectedClientId)
            {
                _logger.LogWarning("Apple token audience validation failed. Expected: {Expected}, Actual: {Actual}",
                    expectedClientId, audience);
                return Task.FromResult<ExternalUserInfo?>(null);
            }

            // 验证过期时间
            if (jsonToken.ValidTo < DateTime.UtcNow)
            {
                _logger.LogWarning("Apple token has expired");
                return Task.FromResult<ExternalUserInfo?>(null);
            }

            // 在生产环境中，这里应该验证 Apple 的 JWT 签名
            // 需要从 https://appleid.apple.com/auth/keys 获取公钥并验证签名
            // 为了简化演示，这里跳过签名验证

            _logger.LogInformation("Apple token validated successfully for user: {Subject}", subject);

            var result = new ExternalUserInfo
            {
                ProviderType = AuthProviderType.Apple,
                ProviderId = subject ?? "",
                Email = email ?? "",
                DisplayName = null, // Apple 通常不在 JWT 中提供姓名
                AvatarUrl = null,   // Apple 不提供头像
                IsEmailVerified = emailVerified == "true"
            };

            return Task.FromResult<ExternalUserInfo?>(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating Apple token");
            return Task.FromResult<ExternalUserInfo?>(null);
        }
    }

    public async Task<bool> ValidateAppleSignatureAsync(string identityToken)
    {
        try
        {
            _logger.LogInformation("Validating Apple token signature");

            // 获取 Apple 的公钥
            var httpClient = _httpClientFactory.CreateClient();
            var response = await httpClient.GetAsync("https://appleid.apple.com/auth/keys");

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Failed to fetch Apple public keys");
                return false;
            }

            var keysJson = await response.Content.ReadAsStringAsync();
            var keys = JsonSerializer.Deserialize<AppleKeysResponse>(keysJson);

            if (keys?.Keys == null || !keys.Keys.Any())
            {
                _logger.LogWarning("No Apple public keys found");
                return false;
            }

            // 解析 JWT header 获取 kid
            var handler = new JwtSecurityTokenHandler();
            var jsonToken = handler.ReadJwtToken(identityToken);
            var kid = jsonToken.Header.Kid;

            if (string.IsNullOrEmpty(kid))
            {
                _logger.LogWarning("Apple token missing kid in header");
                return false;
            }

            // 查找对应的公钥
            var key = keys.Keys.FirstOrDefault(k => k.Kid == kid);
            if (key == null)
            {
                _logger.LogWarning("Apple public key not found for kid: {Kid}", kid);
                return false;
            }

            // 构建 RSA 公钥
            var rsa = RSA.Create();
            rsa.ImportParameters(new RSAParameters
            {
                Modulus = Base64UrlEncoder.DecodeBytes(key.N),
                Exponent = Base64UrlEncoder.DecodeBytes(key.E)
            });

            var securityKey = new RsaSecurityKey(rsa);

            // 验证 JWT 签名
            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = securityKey,
                ValidateIssuer = true,
                ValidIssuer = "https://appleid.apple.com",
                ValidateAudience = true,
                ValidAudience = _configuration["Authentication:Apple:ClientId"],
                ValidateLifetime = true,
                ClockSkew = TimeSpan.FromMinutes(5)
            };

            handler.ValidateToken(identityToken, validationParameters, out _);

            _logger.LogInformation("Apple token signature validated successfully");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating Apple token signature");
            return false;
        }
    }
}

/// <summary>
/// Google Token 信息
/// </summary>
public class GoogleTokenInfo
{
    public string Iss { get; set; } = string.Empty;
    public string Aud { get; set; } = string.Empty;
    public string Sub { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Picture { get; set; } = string.Empty;
    public bool EmailVerified { get; set; }
    public long Exp { get; set; }
}

/// <summary>
/// Apple 公钥响应
/// </summary>
public class AppleKeysResponse
{
    public List<AppleKey> Keys { get; set; } = new();
}

/// <summary>
/// Apple 公钥
/// </summary>
public class AppleKey
{
    public string Kid { get; set; } = string.Empty;
    public string Kty { get; set; } = string.Empty;
    public string Use { get; set; } = string.Empty;
    public string Alg { get; set; } = string.Empty;
    public string N { get; set; } = string.Empty;
    public string E { get; set; } = string.Empty;
}
