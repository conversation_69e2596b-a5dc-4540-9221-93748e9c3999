using Orleans;
using Orleans.Runtime;
using Curios.Grains.Interfaces;
using Curios.Grains.States;
using Curios.Shared.Models;
using Curios.Shared.Utils;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.IdentityModel.Tokens;

namespace Curios.Grains;

/// <summary>
/// 认证 Grain 实现
/// </summary>
public class AuthenticationGrain : Grain, IAuthenticationGrain
{
    private readonly IPersistentState<AuthConfigState> _configState;

    public AuthenticationGrain([PersistentState("authConfig", "userStore")] IPersistentState<AuthConfigState> configState)
    {
        _configState = configState;
    }

    public async Task<AuthenticationResponse> LoginWithEmailAsync(string email, string password)
    {
        try
        {
            // 查找用户
            var user = await FindUserByEmailAsync(email);
            if (user == null)
            {
                return new AuthenticationResponse
                {
                    Success = false,
                    ErrorMessage = "邮箱或密码错误"
                };
            }

            // 验证密码
            var isValidPassword = await VerifyPasswordAsync(user.UserId, password);
            if (!isValidPassword)
            {
                return new AuthenticationResponse
                {
                    Success = false,
                    ErrorMessage = "邮箱或密码错误"
                };
            }

            // 检查用户状态
            if (user.Status != UserStatus.Active)
            {
                return new AuthenticationResponse
                {
                    Success = false,
                    ErrorMessage = "账户已被停用"
                };
            }

            // 更新最后登录时间
            var userGrain = GrainFactory.GetGrain<IUserGrain>(user.UserId);
            await userGrain.UpdateLastLoginAsync();

            // 生成 JWT Token
            var token = await GenerateJwtTokenAsync(user);

            return new AuthenticationResponse
            {
                Success = true,
                User = user,
                Token = token,
                TokenExpiresAt = DateTime.UtcNow.AddHours(24)
            };
        }
        catch (Exception ex)
        {
            return new AuthenticationResponse
            {
                Success = false,
                ErrorMessage = $"登录失败: {ex.Message}"
            };
        }
    }

    public async Task<AuthenticationResponse> LoginWithGoogleAsync(string idToken)
    {
        try
        {
            // 验证 Google ID Token
            var externalAuthGrain = GrainFactory.GetGrain<IExternalAuthGrain>("external");
            var googleUserInfo = await externalAuthGrain.ValidateGoogleTokenAsync(idToken);

            if (googleUserInfo == null)
            {
                return new AuthenticationResponse
                {
                    Success = false,
                    ErrorMessage = "Google 登录验证失败"
                };
            }

            return await ProcessExternalLoginAsync(googleUserInfo);
        }
        catch (Exception ex)
        {
            return new AuthenticationResponse
            {
                Success = false,
                ErrorMessage = $"Google 登录失败: {ex.Message}"
            };
        }
    }

    public async Task<AuthenticationResponse> LoginWithAppleAsync(string identityToken, string authorizationCode, AppleUserInfo? userInfo = null)
    {
        try
        {
            // 验证 Apple Identity Token
            var externalAuthGrain = GrainFactory.GetGrain<IExternalAuthGrain>("external");
            var appleUserInfo = await externalAuthGrain.ValidateAppleTokenAsync(identityToken, authorizationCode);

            if (appleUserInfo == null)
            {
                return new AuthenticationResponse
                {
                    Success = false,
                    ErrorMessage = "Apple 登录验证失败"
                };
            }

            // 如果是首次登录，使用提供的用户信息
            if (userInfo != null && !string.IsNullOrEmpty(userInfo.Email))
            {
                appleUserInfo.Email = userInfo.Email;
                if (userInfo.Name != null)
                {
                    appleUserInfo.DisplayName = $"{userInfo.Name.FirstName} {userInfo.Name.LastName}".Trim();
                }
            }

            return await ProcessExternalLoginAsync(appleUserInfo);
        }
        catch (Exception ex)
        {
            return new AuthenticationResponse
            {
                Success = false,
                ErrorMessage = $"Apple 登录失败: {ex.Message}"
            };
        }
    }

    public async Task<User?> FindUserByEmailAsync(string email)
    {
        // 这里需要实现一个索引机制来通过邮箱查找用户
        // 为了简化，我们先使用一个简单的方法
        // 在实际生产环境中，应该使用专门的索引 Grain 或数据库查询

        // 临时实现：遍历可能的用户ID（这不是最优解）
        // 实际应该有一个 EmailToUserIdGrain 来维护邮箱到用户ID的映射
        var emailIndexGrain = GrainFactory.GetGrain<IEmailIndexGrain>(email);
        var userId = await emailIndexGrain.GetUserIdAsync();

        if (string.IsNullOrEmpty(userId))
        {
            return null;
        }

        var userGrain = GrainFactory.GetGrain<IUserGrain>(userId);
        return await userGrain.GetUserAsync();
    }

    public async Task<User?> FindUserByProviderAsync(AuthProviderType providerType, string providerId)
    {
        // 类似于邮箱查找，这里也需要索引机制
        var providerIndexGrain = GrainFactory.GetGrain<IProviderIndexGrain>($"{providerType}:{providerId}");
        var userId = await providerIndexGrain.GetUserIdAsync();

        if (string.IsNullOrEmpty(userId))
        {
            return null;
        }

        var userGrain = GrainFactory.GetGrain<IUserGrain>(userId);
        return await userGrain.GetUserAsync();
    }

    public async Task<bool> VerifyPasswordAsync(string userId, string password)
    {
        var userGrain = GrainFactory.GetGrain<IUserGrain>(userId);
        var authProvider = await userGrain.GetAuthProviderAsync(AuthProviderType.Email);

        if (authProvider?.PasswordHash == null || authProvider.PasswordSalt == null)
        {
            return false;
        }

        return PasswordHasher.VerifyPassword(password, authProvider.PasswordHash, authProvider.PasswordSalt);
    }

    public async Task<bool> UpdatePasswordAsync(string userId, string newPassword)
    {
        var userGrain = GrainFactory.GetGrain<IUserGrain>(userId);
        var authProvider = await userGrain.GetAuthProviderAsync(AuthProviderType.Email);

        if (authProvider == null)
        {
            return false;
        }

        var (passwordHash, passwordSalt) = PasswordHasher.HashPassword(newPassword);
        authProvider.PasswordHash = passwordHash;
        authProvider.PasswordSalt = passwordSalt;
        authProvider.LastUsedAt = DateTime.UtcNow;

        return await userGrain.AddAuthProviderAsync(authProvider);
    }

    public async Task<string> GenerateJwtTokenAsync(User user)
    {
        await EnsureConfigInitializedAsync();

        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.ASCII.GetBytes(_configState.State.JwtSecret);

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(new[]
            {
                new Claim("user_id", user.UserId),
                new Claim("email", user.Email),
                new Claim("display_name", user.DisplayName ?? ""),
                new Claim("iat", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64),
            }),
            Expires = DateTime.UtcNow.AddHours(_configState.State.JwtExpirationHours),
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

    public async Task<Curios.Shared.Models.JwtPayload?> ValidateJwtTokenAsync(string token)
    {
        try
        {
            await EnsureConfigInitializedAsync();

            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_configState.State.JwtSecret);

            tokenHandler.ValidateToken(token, new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = false,
                ValidateAudience = false,
                ClockSkew = TimeSpan.Zero
            }, out SecurityToken validatedToken);

            var jwtToken = (JwtSecurityToken)validatedToken;

            return new Curios.Shared.Models.JwtPayload
            {
                UserId = jwtToken.Claims.First(x => x.Type == "user_id").Value,
                Email = jwtToken.Claims.First(x => x.Type == "email").Value,
                DisplayName = jwtToken.Claims.FirstOrDefault(x => x.Type == "display_name")?.Value,
                Iat = long.Parse(jwtToken.Claims.First(x => x.Type == "iat").Value),
                Exp = ((DateTimeOffset)jwtToken.ValidTo).ToUnixTimeSeconds()
            };
        }
        catch
        {
            return null;
        }
    }

    public Task<string> RefreshJwtTokenAsync(string refreshToken)
    {
        // TODO: 实现刷新 Token 逻辑
        throw new NotImplementedException("刷新 Token 功能待实现");
    }

    private async Task<AuthenticationResponse> ProcessExternalLoginAsync(ExternalUserInfo externalUserInfo)
    {
        // 查找现有用户
        var existingUser = await FindUserByProviderAsync(externalUserInfo.ProviderType, externalUserInfo.ProviderId);

        if (existingUser != null)
        {
            // 用户已存在，直接登录
            var userGrain = GrainFactory.GetGrain<IUserGrain>(existingUser.UserId);
            await userGrain.UpdateLastLoginAsync();

            var token = await GenerateJwtTokenAsync(existingUser);

            return new AuthenticationResponse
            {
                Success = true,
                User = existingUser,
                Token = token,
                TokenExpiresAt = DateTime.UtcNow.AddHours(24)
            };
        }

        // 检查邮箱是否已被其他方式注册
        var userByEmail = await FindUserByEmailAsync(externalUserInfo.Email);
        if (userByEmail != null)
        {
            // 邮箱已存在，将第三方认证绑定到现有账户
            var authProvider = new AuthenticationProvider
            {
                ProviderType = externalUserInfo.ProviderType,
                ProviderId = externalUserInfo.ProviderId,
                ProviderEmail = externalUserInfo.Email,
                CreatedAt = DateTime.UtcNow,
                LastUsedAt = DateTime.UtcNow
            };

            var userGrain = GrainFactory.GetGrain<IUserGrain>(userByEmail.UserId);
            await userGrain.AddAuthProviderAsync(authProvider);
            await userGrain.UpdateLastLoginAsync();

            var token = await GenerateJwtTokenAsync(userByEmail);

            return new AuthenticationResponse
            {
                Success = true,
                User = userByEmail,
                Token = token,
                TokenExpiresAt = DateTime.UtcNow.AddHours(24)
            };
        }

        // 创建新用户
        var userId = Guid.NewGuid().ToString();
        var newUserGrain = GrainFactory.GetGrain<IUserGrain>(userId);
        var newUser = await newUserGrain.CreateUserAsync(
            externalUserInfo.Email,
            externalUserInfo.DisplayName,
            externalUserInfo.AvatarUrl);

        // 添加第三方认证提供商
        var newAuthProvider = new AuthenticationProvider
        {
            ProviderType = externalUserInfo.ProviderType,
            ProviderId = externalUserInfo.ProviderId,
            ProviderEmail = externalUserInfo.Email,
            CreatedAt = DateTime.UtcNow,
            LastUsedAt = DateTime.UtcNow
        };

        await newUserGrain.AddAuthProviderAsync(newAuthProvider);

        // 如果第三方提供商已验证邮箱，则标记为已验证
        if (externalUserInfo.IsEmailVerified)
        {
            await newUserGrain.VerifyEmailAsync();
        }

        await newUserGrain.UpdateLastLoginAsync();

        var newToken = await GenerateJwtTokenAsync(newUser);

        return new AuthenticationResponse
        {
            Success = true,
            User = newUser,
            Token = newToken,
            TokenExpiresAt = DateTime.UtcNow.AddHours(24)
        };
    }

    private async Task EnsureConfigInitializedAsync()
    {
        if (string.IsNullOrEmpty(_configState.State.JwtSecret))
        {
            // 初始化默认配置
            _configState.State.JwtSecret = PasswordHasher.GenerateRandomString(64);
            _configState.State.JwtExpirationHours = 24;
            _configState.State.RefreshTokenExpirationDays = 30;

            await _configState.WriteStateAsync();
        }
    }
}

// 临时的索引 Grain 接口，实际项目中应该放在 Interfaces 项目中
public interface IEmailIndexGrain : IGrainWithStringKey
{
    Task<string?> GetUserIdAsync();
    Task SetUserIdAsync(string userId);
    Task RemoveAsync();
}

public interface IProviderIndexGrain : IGrainWithStringKey
{
    Task<string?> GetUserIdAsync();
    Task SetUserIdAsync(string userId);
    Task RemoveAsync();
}
