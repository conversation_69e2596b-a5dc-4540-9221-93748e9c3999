﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.8" />
    <PackageReference Include="Microsoft.Orleans.Core" Version="9.2.1" />
    <PackageReference Include="Microsoft.Orleans.Persistence.Memory" Version="9.2.1" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.13.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Curios.Grains.Interfaces\Curios.Grains.Interfaces.csproj" />
    <ProjectReference Include="..\Curios.Shared\Curios.Shared.csproj" />
  </ItemGroup>

</Project>
