using Microsoft.AspNetCore.Mvc;
using Orleans;

namespace Curios.Api.Controllers;

/// <summary>
/// 健康检查控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class HealthController : ControllerBase
{
    private readonly IClusterClient _clusterClient;
    private readonly ILogger<HealthController> _logger;

    public HealthController(IClusterClient clusterClient, ILogger<HealthController> logger)
    {
        _clusterClient = clusterClient;
        _logger = logger;
    }

    /// <summary>
    /// 基本健康检查
    /// </summary>
    /// <returns>健康状态</returns>
    [HttpGet]
    public ActionResult GetHealth()
    {
        return Ok(new
        {
            status = "healthy",
            timestamp = DateTime.UtcNow,
            version = "1.0.0"
        });
    }

    /// <summary>
    /// 详细健康检查（包括 Orleans 连接）
    /// </summary>
    /// <returns>详细健康状态</returns>
    [HttpGet("detailed")]
    public async Task<ActionResult> GetDetailedHealthAsync()
    {
        try
        {
            var healthInfo = new
            {
                status = "healthy",
                timestamp = DateTime.UtcNow,
                version = "1.0.0",
                orleans = await CheckOrleansHealthAsync(),
                environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown"
            };

            return Ok(healthInfo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed");
            
            return StatusCode(503, new
            {
                status = "unhealthy",
                timestamp = DateTime.UtcNow,
                error = ex.Message
            });
        }
    }

    private async Task<object> CheckOrleansHealthAsync()
    {
        try
        {
            // 尝试获取一个测试 Grain 来验证 Orleans 连接
            var testGrain = _clusterClient.GetGrain<Curios.Grains.Interfaces.IUserGrain>("health-check");
            var exists = await testGrain.ExistsAsync();
            
            return new
            {
                status = "connected",
                clusterId = "curios-cluster",
                serviceId = "curios-service"
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Orleans health check failed");
            
            return new
            {
                status = "disconnected",
                error = ex.Message
            };
        }
    }
}
