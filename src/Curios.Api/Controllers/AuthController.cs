using Microsoft.AspNetCore.Mvc;
using Orleans;
using Curios.Grains.Interfaces;
using Curios.Shared.Models;

namespace Curios.Api.Controllers;

/// <summary>
/// 认证控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class AuthController : ControllerBase
{
    private readonly IClusterClient _clusterClient;
    private readonly ILogger<AuthController> _logger;

    public AuthController(IClusterClient clusterClient, ILogger<AuthController> logger)
    {
        _clusterClient = clusterClient;
        _logger = logger;
    }

    /// <summary>
    /// 发送邮箱注册验证码
    /// </summary>
    /// <param name="request">邮箱注册请求</param>
    /// <returns>验证码发送结果</returns>
    /// <response code="200">验证码发送成功</response>
    /// <response code="400">请求参数错误或邮箱已被注册</response>
    /// <response code="500">服务器内部错误</response>
    [HttpPost("email/send-verification")]
    [ProducesResponseType(typeof(EmailVerificationResponse), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<EmailVerificationResponse>> SendEmailVerificationAsync([FromBody] EmailRegistrationRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // 检查邮箱是否已被注册
            var authGrain = _clusterClient.GetGrain<IAuthenticationGrain>("auth");
            var existingUser = await authGrain.FindUserByEmailAsync(request.Email);
            if (existingUser != null)
            {
                return BadRequest(new { message = "该邮箱已被注册" });
            }

            // 发送验证码
            var emailVerificationGrain = _clusterClient.GetGrain<IEmailVerificationGrain>("email-verification");
            var result = await emailVerificationGrain.SendVerificationCodeAsync(request.Email, VerificationType.EmailRegistration);

            if (result.Success)
            {
                _logger.LogInformation("Verification code sent to {Email}", request.Email);
                return Ok(result);
            }
            else
            {
                _logger.LogWarning("Failed to send verification code to {Email}: {Error}", request.Email, result.ErrorMessage);
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending verification code to {Email}", request.Email);
            return StatusCode(500, new { message = "发送验证码时发生错误" });
        }
    }

    /// <summary>
    /// 完成邮箱注册
    /// </summary>
    /// <param name="request">邮箱验证请求</param>
    /// <returns>注册结果</returns>
    [HttpPost("email/register")]
    public async Task<ActionResult<AuthenticationResponse>> CompleteEmailRegistrationAsync([FromBody] EmailVerificationRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var emailVerificationGrain = _clusterClient.GetGrain<IEmailVerificationGrain>("email-verification");
            var result = await emailVerificationGrain.CompleteEmailRegistrationAsync(
                request.Email,
                request.VerificationCode,
                request.Password,
                request.DisplayName);

            if (result.Success)
            {
                _logger.LogInformation("User registered successfully with email {Email}", request.Email);
                return Ok(result);
            }
            else
            {
                _logger.LogWarning("Email registration failed for {Email}: {Error}", request.Email, result.ErrorMessage);
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error completing email registration for {Email}", request.Email);
            return StatusCode(500, new { message = "注册时发生错误" });
        }
    }

    /// <summary>
    /// 邮箱密码登录
    /// </summary>
    /// <param name="request">邮箱登录请求</param>
    /// <returns>登录结果</returns>
    [HttpPost("email/login")]
    public async Task<ActionResult<AuthenticationResponse>> EmailLoginAsync([FromBody] EmailLoginRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var authGrain = _clusterClient.GetGrain<IAuthenticationGrain>("auth");
            var result = await authGrain.LoginWithEmailAsync(request.Email, request.Password);

            if (result.Success)
            {
                _logger.LogInformation("User logged in successfully with email {Email}", request.Email);
                return Ok(result);
            }
            else
            {
                _logger.LogWarning("Email login failed for {Email}: {Error}", request.Email, result.ErrorMessage);
                return Unauthorized(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during email login for {Email}", request.Email);
            return StatusCode(500, new { message = "登录时发生错误" });
        }
    }

    /// <summary>
    /// Google 登录
    /// </summary>
    /// <param name="request">Google 登录请求</param>
    /// <returns>登录结果</returns>
    [HttpPost("google/login")]
    public async Task<ActionResult<AuthenticationResponse>> GoogleLoginAsync([FromBody] GoogleLoginRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var authGrain = _clusterClient.GetGrain<IAuthenticationGrain>("auth");
            var result = await authGrain.LoginWithGoogleAsync(request.IdToken);

            if (result.Success)
            {
                _logger.LogInformation("User logged in successfully with Google");
                return Ok(result);
            }
            else
            {
                _logger.LogWarning("Google login failed: {Error}", result.ErrorMessage);
                return Unauthorized(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during Google login");
            return StatusCode(500, new { message = "Google 登录时发生错误" });
        }
    }

    /// <summary>
    /// Apple 登录
    /// </summary>
    /// <param name="request">Apple 登录请求</param>
    /// <returns>登录结果</returns>
    [HttpPost("apple/login")]
    public async Task<ActionResult<AuthenticationResponse>> AppleLoginAsync([FromBody] AppleLoginRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var authGrain = _clusterClient.GetGrain<IAuthenticationGrain>("auth");
            var result = await authGrain.LoginWithAppleAsync(
                request.IdentityToken,
                request.AuthorizationCode,
                request.User);

            if (result.Success)
            {
                _logger.LogInformation("User logged in successfully with Apple");
                return Ok(result);
            }
            else
            {
                _logger.LogWarning("Apple login failed: {Error}", result.ErrorMessage);
                return Unauthorized(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during Apple login");
            return StatusCode(500, new { message = "Apple 登录时发生错误" });
        }
    }

    /// <summary>
    /// 验证 JWT Token
    /// </summary>
    /// <param name="token">JWT Token</param>
    /// <returns>Token 验证结果</returns>
    [HttpPost("validate-token")]
    public async Task<ActionResult<JwtPayload>> ValidateTokenAsync([FromBody] string token)
    {
        try
        {
            if (string.IsNullOrEmpty(token))
            {
                return BadRequest(new { message = "Token 不能为空" });
            }

            var authGrain = _clusterClient.GetGrain<IAuthenticationGrain>("auth");
            var payload = await authGrain.ValidateJwtTokenAsync(token);

            if (payload != null)
            {
                return Ok(payload);
            }
            else
            {
                return Unauthorized(new { message = "Token 无效或已过期" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating token");
            return StatusCode(500, new { message = "验证 Token 时发生错误" });
        }
    }
}
