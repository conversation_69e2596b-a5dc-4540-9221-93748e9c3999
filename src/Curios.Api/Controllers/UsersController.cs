using Microsoft.AspNetCore.Mvc;
using Orleans;
using Curios.Grains.Interfaces;
using Curios.Shared.Models;
using Curios.Api.Middleware;

namespace Curios.Api.Controllers;

/// <summary>
/// 用户管理控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class UsersController : AuthenticatedControllerBase
{
    private readonly IClusterClient _clusterClient;
    private readonly ILogger<UsersController> _logger;

    public UsersController(IClusterClient clusterClient, ILogger<UsersController> logger)
    {
        _clusterClient = clusterClient;
        _logger = logger;
    }

    /// <summary>
    /// 获取用户信息
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>用户信息</returns>
    [HttpGet("{userId}")]
    public async Task<ActionResult<User>> GetUserAsync(string userId)
    {
        try
        {
            // 权限检查：只能访问自己的信息
            if (!CanAccessUser(userId))
            {
                return Forbid("无权访问该用户信息");
            }

            var userGrain = _clusterClient.GetGrain<IUserGrain>(userId);
            var user = await userGrain.GetUserAsync();

            if (user == null)
            {
                return NotFound(new { message = "用户不存在" });
            }

            return Ok(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user {UserId}", userId);
            return StatusCode(500, new { message = "获取用户信息时发生错误" });
        }
    }

    /// <summary>
    /// 更新用户信息
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="request">更新请求</param>
    /// <returns>更新后的用户信息</returns>
    [HttpPut("{userId}")]
    public async Task<ActionResult<User>> UpdateUserAsync(string userId, [FromBody] UpdateUserRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var userGrain = _clusterClient.GetGrain<IUserGrain>(userId);
            var exists = await userGrain.ExistsAsync();

            if (!exists)
            {
                return NotFound(new { message = "用户不存在" });
            }

            var updatedUser = await userGrain.UpdateUserAsync(request.DisplayName, request.AvatarUrl);

            _logger.LogInformation("User {UserId} updated successfully", userId);
            return Ok(updatedUser);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user {UserId}", userId);
            return StatusCode(500, new { message = "更新用户信息时发生错误" });
        }
    }

    /// <summary>
    /// 停用用户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>操作结果</returns>
    [HttpPost("{userId}/deactivate")]
    public async Task<ActionResult> DeactivateUserAsync(string userId)
    {
        try
        {
            var userGrain = _clusterClient.GetGrain<IUserGrain>(userId);
            var exists = await userGrain.ExistsAsync();

            if (!exists)
            {
                return NotFound(new { message = "用户不存在" });
            }

            var success = await userGrain.DeactivateUserAsync();

            if (success)
            {
                _logger.LogInformation("User {UserId} deactivated successfully", userId);
                return Ok(new { message = "用户已停用" });
            }
            else
            {
                return BadRequest(new { message = "停用用户失败" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating user {UserId}", userId);
            return StatusCode(500, new { message = "停用用户时发生错误" });
        }
    }

    /// <summary>
    /// 激活用户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>操作结果</returns>
    [HttpPost("{userId}/activate")]
    public async Task<ActionResult> ActivateUserAsync(string userId)
    {
        try
        {
            var userGrain = _clusterClient.GetGrain<IUserGrain>(userId);
            var exists = await userGrain.ExistsAsync();

            if (!exists)
            {
                return NotFound(new { message = "用户不存在" });
            }

            var success = await userGrain.ActivateUserAsync();

            if (success)
            {
                _logger.LogInformation("User {UserId} activated successfully", userId);
                return Ok(new { message = "用户已激活" });
            }
            else
            {
                return BadRequest(new { message = "激活用户失败" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating user {UserId}", userId);
            return StatusCode(500, new { message = "激活用户时发生错误" });
        }
    }

    /// <summary>
    /// 删除用户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>操作结果</returns>
    [HttpDelete("{userId}")]
    public async Task<ActionResult> DeleteUserAsync(string userId)
    {
        try
        {
            var userGrain = _clusterClient.GetGrain<IUserGrain>(userId);
            var exists = await userGrain.ExistsAsync();

            if (!exists)
            {
                return NotFound(new { message = "用户不存在" });
            }

            var success = await userGrain.DeleteUserAsync();

            if (success)
            {
                _logger.LogInformation("User {UserId} deleted successfully", userId);
                return Ok(new { message = "用户已删除" });
            }
            else
            {
                return BadRequest(new { message = "删除用户失败" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user {UserId}", userId);
            return StatusCode(500, new { message = "删除用户时发生错误" });
        }
    }

    /// <summary>
    /// 验证用户邮箱
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>操作结果</returns>
    [HttpPost("{userId}/verify-email")]
    public async Task<ActionResult> VerifyEmailAsync(string userId)
    {
        try
        {
            var userGrain = _clusterClient.GetGrain<IUserGrain>(userId);
            var exists = await userGrain.ExistsAsync();

            if (!exists)
            {
                return NotFound(new { message = "用户不存在" });
            }

            var success = await userGrain.VerifyEmailAsync();

            if (success)
            {
                _logger.LogInformation("Email verified for user {UserId}", userId);
                return Ok(new { message = "邮箱验证成功" });
            }
            else
            {
                return BadRequest(new { message = "邮箱验证失败" });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying email for user {UserId}", userId);
            return StatusCode(500, new { message = "验证邮箱时发生错误" });
        }
    }

    /// <summary>
    /// 获取用户的认证提供商
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="providerType">提供商类型</param>
    /// <returns>认证提供商信息</returns>
    [HttpGet("{userId}/auth-providers/{providerType}")]
    public async Task<ActionResult<AuthenticationProvider>> GetAuthProviderAsync(string userId, AuthProviderType providerType)
    {
        try
        {
            var userGrain = _clusterClient.GetGrain<IUserGrain>(userId);
            var exists = await userGrain.ExistsAsync();

            if (!exists)
            {
                return NotFound(new { message = "用户不存在" });
            }

            var authProvider = await userGrain.GetAuthProviderAsync(providerType);

            if (authProvider == null)
            {
                return NotFound(new { message = "认证提供商不存在" });
            }

            // 不返回敏感信息
            authProvider.PasswordHash = null;
            authProvider.PasswordSalt = null;

            return Ok(authProvider);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting auth provider for user {UserId}", userId);
            return StatusCode(500, new { message = "获取认证提供商时发生错误" });
        }
    }
}

/// <summary>
/// 更新用户请求
/// </summary>
public class UpdateUserRequest
{
    /// <summary>
    /// 显示名称
    /// </summary>
    public string? DisplayName { get; set; }

    /// <summary>
    /// 头像URL
    /// </summary>
    public string? AvatarUrl { get; set; }
}
