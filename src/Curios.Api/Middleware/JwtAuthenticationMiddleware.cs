using Orleans;
using Curios.Grains.Interfaces;
using System.Security.Claims;

namespace Curios.Api.Middleware;

/// <summary>
/// JWT 认证中间件
/// </summary>
public class JwtAuthenticationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<JwtAuthenticationMiddleware> _logger;

    public JwtAuthenticationMiddleware(RequestDelegate next, ILogger<JwtAuthenticationMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, IClusterClient clusterClient)
    {
        // 跳过不需要认证的端点
        if (ShouldSkipAuthentication(context.Request.Path))
        {
            await _next(context);
            return;
        }

        var token = ExtractTokenFromHeader(context.Request);

        if (string.IsNullOrEmpty(token))
        {
            context.Response.StatusCode = 401;
            await context.Response.WriteAsync("Missing or invalid authorization header");
            return;
        }

        try
        {
            var authGrain = clusterClient.GetGrain<IAuthenticationGrain>("auth");
            var payload = await authGrain.ValidateJwtTokenAsync(token);

            if (payload == null)
            {
                context.Response.StatusCode = 401;
                await context.Response.WriteAsync("Invalid or expired token");
                return;
            }

            // 设置用户身份信息
            var claims = new[]
            {
                new Claim("user_id", payload.UserId),
                new Claim("email", payload.Email),
                new Claim("display_name", payload.DisplayName ?? ""),
                new Claim("iat", payload.Iat.ToString()),
                new Claim("exp", payload.Exp.ToString())
            };

            var identity = new ClaimsIdentity(claims, "jwt");
            context.User = new ClaimsPrincipal(identity);

            // 将用户ID添加到请求头中，方便控制器使用
            context.Items["UserId"] = payload.UserId;
            context.Items["UserEmail"] = payload.Email;

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating JWT token");
            context.Response.StatusCode = 401;
            await context.Response.WriteAsync("Token validation failed");
        }
    }

    private static string? ExtractTokenFromHeader(HttpRequest request)
    {
        var authHeader = request.Headers.Authorization.FirstOrDefault();

        if (string.IsNullOrEmpty(authHeader))
        {
            return null;
        }

        if (authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
        {
            return authHeader.Substring("Bearer ".Length).Trim();
        }

        return null;
    }

    private static bool ShouldSkipAuthentication(PathString path)
    {
        var publicPaths = new[]
        {
            "/api/auth/email/send-verification",
            "/api/auth/email/register",
            "/api/auth/email/login",
            "/api/auth/google/login",
            "/api/auth/apple/login",
            "/api/auth/validate-token",
            "/api/health",
            "/weatherforecast",
            "/health",
            "/alive",
            "/ready",
            "/swagger",
            "/openapi"
        };

        return publicPaths.Any(publicPath =>
            path.StartsWithSegments(publicPath, StringComparison.OrdinalIgnoreCase));
    }
}

/// <summary>
/// JWT 认证中间件扩展
/// </summary>
public static class JwtAuthenticationMiddlewareExtensions
{
    /// <summary>
    /// 添加 JWT 认证中间件
    /// </summary>
    /// <param name="builder">应用程序构建器</param>
    /// <returns>应用程序构建器</returns>
    public static IApplicationBuilder UseJwtAuthentication(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<JwtAuthenticationMiddleware>();
    }
}

/// <summary>
/// 控制器基类，提供用户信息访问
/// </summary>
public abstract class AuthenticatedControllerBase : Microsoft.AspNetCore.Mvc.ControllerBase
{
    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    protected string? CurrentUserId => HttpContext.Items["UserId"]?.ToString();

    /// <summary>
    /// 获取当前用户邮箱
    /// </summary>
    protected string? CurrentUserEmail => HttpContext.Items["UserEmail"]?.ToString();

    /// <summary>
    /// 检查是否为当前用户或管理员
    /// </summary>
    /// <param name="userId">要检查的用户ID</param>
    /// <returns>是否有权限</returns>
    protected bool CanAccessUser(string userId)
    {
        // 简单的权限检查：只能访问自己的信息
        // 在实际项目中，这里可以添加更复杂的权限逻辑
        return CurrentUserId == userId;
    }
}
