# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
USER app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["src/Curios.Api/Curios.ApiService.csproj", "src/Curios.Api/"]
COPY ["src/Curios.Grains.Interfaces/Curios.Grains.Interfaces.csproj", "src/Curios.Grains.Interfaces/"]
COPY ["src/Curios.ServiceDefaults/Curios.ServiceDefaults.csproj", "src/Curios.ServiceDefaults/"]
RUN dotnet restore "./src/Curios.Api/Curios.ApiService.csproj"
COPY . .
WORKDIR "/src/src/Curios.Api"
RUN dotnet build "./Curios.ApiService.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./Curios.ApiService.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Curios.ApiService.dll"]
