@ApiService_HostAddress = http://localhost:5314
@token = your_jwt_token_here

### Health Check
GET {{ApiService_HostAddress}}/api/health
Accept: application/json

### Detailed Health Check
GET {{ApiService_HostAddress}}/api/health/detailed
Accept: application/json

### Weather Forecast (for testing)
GET {{ApiService_HostAddress}}/weatherforecast/
Accept: application/json

### 1. Send Email Verification Code
POST {{ApiService_HostAddress}}/api/auth/email/send-verification
Content-Type: application/json

{
  "email": "<EMAIL>"
}

### 2. Complete Email Registration
POST {{ApiService_HostAddress}}/api/auth/email/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "verificationCode": "123456",
  "password": "SecurePassword123!",
  "displayName": "Test User"
}

### 3. Email Login
POST {{ApiService_HostAddress}}/api/auth/email/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}

### 4. Google Login
POST {{ApiService_HostAddress}}/api/auth/google/login
Content-Type: application/json

{
  "idToken": "google_id_token_here"
}

### 5. Apple Login
POST {{ApiService_HostAddress}}/api/auth/apple/login
Content-Type: application/json

{
  "identityToken": "apple_identity_token_here",
  "authorizationCode": "apple_auth_code_here",
  "user": {
    "name": {
      "firstName": "John",
      "lastName": "Doe"
    },
    "email": "<EMAIL>"
  }
}

### 6. Validate JWT Token
POST {{ApiService_HostAddress}}/api/auth/validate-token
Content-Type: application/json

"{{token}}"

### 7. Get User Info (requires authentication)
GET {{ApiService_HostAddress}}/api/users/user-id-here
Authorization: Bearer {{token}}
Accept: application/json

### 8. Update User Info (requires authentication)
PUT {{ApiService_HostAddress}}/api/users/user-id-here
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "displayName": "Updated Name",
  "avatarUrl": "https://example.com/avatar.jpg"
}

### 9. Deactivate User (requires authentication)
POST {{ApiService_HostAddress}}/api/users/user-id-here/deactivate
Authorization: Bearer {{token}}

### 10. Activate User (requires authentication)
POST {{ApiService_HostAddress}}/api/users/user-id-here/activate
Authorization: Bearer {{token}}

### 11. Verify Email (requires authentication)
POST {{ApiService_HostAddress}}/api/users/user-id-here/verify-email
Authorization: Bearer {{token}}

### 12. Get Auth Provider (requires authentication)
GET {{ApiService_HostAddress}}/api/users/user-id-here/auth-providers/Email
Authorization: Bearer {{token}}
Accept: application/json

### 13. Delete User (requires authentication)
DELETE {{ApiService_HostAddress}}/api/users/user-id-here
Authorization: Bearer {{token}}