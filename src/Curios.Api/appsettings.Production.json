{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Error", "Orleans": "Warning"}, "Seq": {"ServerUrl": "http://seq:80"}}, "OpenTelemetry": {"Jaeger": {"Endpoint": "http://jaeger:14268/api/traces"}}, "AllowedHosts": "*", "ConnectionStrings": {"redis": "redis:6379", "orleansdb": "Host=postgres;Database=orleansdb;Username=orleans;Password=**********"}, "Orleans": {"ClusterId": "curios-prod-cluster", "ServiceId": "curios-prod-service"}}