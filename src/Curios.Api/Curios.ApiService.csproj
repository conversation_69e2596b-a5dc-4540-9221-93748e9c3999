<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Curios.ServiceDefaults\Curios.ServiceDefaults.csproj" />
    <ProjectReference Include="..\Curios.Grains.Interfaces\Curios.Grains.Interfaces.csproj" />
    <ProjectReference Include="..\Curios.Shared\Curios.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
    <PackageReference Include="Microsoft.Orleans.Client" Version="9.2.1" />
    <PackageReference Include="Microsoft.Orleans.Clustering.Redis" Version="9.2.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
  </ItemGroup>

</Project>
