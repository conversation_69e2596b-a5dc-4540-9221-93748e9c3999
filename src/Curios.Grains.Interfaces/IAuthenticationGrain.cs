using Orleans;
using Curios.Shared.Models;

namespace Curios.Grains.Interfaces;

/// <summary>
/// 认证 Grain 接口
/// </summary>
public interface IAuthenticationGrain : IGrainWithStringKey
{
    /// <summary>
    /// 邮箱密码登录
    /// </summary>
    Task<AuthenticationResponse> LoginWithEmailAsync(string email, string password);

    /// <summary>
    /// Google 登录
    /// </summary>
    Task<AuthenticationResponse> LoginWithGoogleAsync(string idToken);

    /// <summary>
    /// Apple 登录
    /// </summary>
    Task<AuthenticationResponse> LoginWithAppleAsync(string identityToken, string authorizationCode, AppleUserInfo? userInfo = null);

    /// <summary>
    /// 通过邮箱查找用户
    /// </summary>
    Task<User?> FindUserByEmailAsync(string email);

    /// <summary>
    /// 通过提供商查找用户
    /// </summary>
    Task<User?> FindUserByProviderAsync(AuthProviderType providerType, string providerId);

    /// <summary>
    /// 验证密码
    /// </summary>
    Task<bool> VerifyPasswordAsync(string userId, string password);

    /// <summary>
    /// 更新密码
    /// </summary>
    Task<bool> UpdatePasswordAsync(string userId, string newPassword);

    /// <summary>
    /// 生成 JWT Token
    /// </summary>
    Task<string> GenerateJwtTokenAsync(User user);

    /// <summary>
    /// 验证 JWT Token
    /// </summary>
    Task<JwtPayload?> ValidateJwtTokenAsync(string token);

    /// <summary>
    /// 刷新 JWT Token
    /// </summary>
    Task<string> RefreshJwtTokenAsync(string refreshToken);
}

/// <summary>
/// 邮箱验证 Grain 接口
/// </summary>
public interface IEmailVerificationGrain : IGrainWithStringKey
{
    /// <summary>
    /// 发送验证码
    /// </summary>
    Task<EmailVerificationResponse> SendVerificationCodeAsync(string email, VerificationType type);

    /// <summary>
    /// 验证验证码
    /// </summary>
    Task<bool> VerifyCodeAsync(string email, string code);

    /// <summary>
    /// 完成邮箱注册
    /// </summary>
    Task<AuthenticationResponse> CompleteEmailRegistrationAsync(string email, string code, string password, string? displayName = null);

    /// <summary>
    /// 获取验证记录
    /// </summary>
    Task<EmailVerification?> GetVerificationAsync(string email);

    /// <summary>
    /// 清理过期验证码
    /// </summary>
    Task CleanupExpiredCodesAsync();
}

/// <summary>
/// 第三方认证服务 Grain 接口
/// </summary>
public interface IExternalAuthGrain : IGrainWithStringKey
{
    /// <summary>
    /// 验证 Google ID Token
    /// </summary>
    Task<ExternalUserInfo?> ValidateGoogleTokenAsync(string idToken);

    /// <summary>
    /// 验证 Apple Identity Token
    /// </summary>
    Task<ExternalUserInfo?> ValidateAppleTokenAsync(string identityToken, string authorizationCode);
}
