using Orleans;
using Curios.Shared.Models;

namespace Curios.Grains.Interfaces;

/// <summary>
/// 用户 Grain 接口
/// </summary>
public interface IUserGrain : IGrainWithStringKey
{
    /// <summary>
    /// 获取用户信息
    /// </summary>
    Task<User?> GetUserAsync();

    /// <summary>
    /// 创建用户
    /// </summary>
    Task<User> CreateUserAsync(string email, string? displayName = null, string? avatarUrl = null);

    /// <summary>
    /// 更新用户信息
    /// </summary>
    Task<User> UpdateUserAsync(string? displayName = null, string? avatarUrl = null);

    /// <summary>
    /// 添加认证提供商
    /// </summary>
    Task<bool> AddAuthProviderAsync(AuthenticationProvider provider);

    /// <summary>
    /// 移除认证提供商
    /// </summary>
    Task<bool> RemoveAuthProviderAsync(AuthProviderType providerType);

    /// <summary>
    /// 获取认证提供商
    /// </summary>
    Task<AuthenticationProvider?> GetAuthProviderAsync(AuthProviderType providerType);

    /// <summary>
    /// 验证邮箱
    /// </summary>
    Task<bool> VerifyEmailAsync();

    /// <summary>
    /// 更新最后登录时间
    /// </summary>
    Task UpdateLastLoginAsync();

    /// <summary>
    /// 停用用户
    /// </summary>
    Task<bool> DeactivateUserAsync();

    /// <summary>
    /// 激活用户
    /// </summary>
    Task<bool> ActivateUserAsync();

    /// <summary>
    /// 删除用户
    /// </summary>
    Task<bool> DeleteUserAsync();

    /// <summary>
    /// 检查用户是否存在
    /// </summary>
    Task<bool> ExistsAsync();
}
