var builder = DistributedApplication.CreateBuilder(args);

// Add Redis for Orleans clustering
var redis = builder.AddRedis("redis")
    .WithRedisCommander();

// Add PostgreSQL for Orleans persistence
var postgres = builder.AddPostgres("postgres")
    .WithPgAdmin()
    .AddDatabase("orleansdb");

// Add Orleans Silo - must start first and be healthy
var silo = builder.AddProject<Projects.Curios_Silo>("silo")
    .WithReference(redis)
    .WithReference(postgres)
    .WaitFor(redis)
    .WaitFor(postgres);

// Add API Service with Orleans Client - waits for silo to be running
builder.AddProject<Projects.Curios_ApiService>("api")
    .WithReference(redis)
    .WithReference(silo)  // This creates the dependency
    .WaitFor(redis)
    .WaitFor(silo);       // This ensures API waits for silo to be ready

var app = builder.Build();
app.Run();
