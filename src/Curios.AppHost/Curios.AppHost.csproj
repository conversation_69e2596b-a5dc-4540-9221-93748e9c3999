<Project Sdk="Microsoft.NET.Sdk">

  <Sdk Name="Aspire.AppHost.Sdk" Version="9.4.0" />

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <UserSecretsId>c61b6e16-cec8-439a-b5ba-e6817a65d480</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Curios.Api\Curios.ApiService.csproj" />
    <ProjectReference Include="..\Curios.Silo\Curios.Silo.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.PostgreSQL" Version="9.4.0" />
    <PackageReference Include="Aspire.Hosting.Redis" Version="9.4.0" />
  </ItemGroup>

</Project>
