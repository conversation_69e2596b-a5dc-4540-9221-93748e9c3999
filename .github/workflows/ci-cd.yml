name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  workflow_dispatch: # 允许手动触发

env:
  DOTNET_VERSION: "9.0.x"
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    name: Test

    steps:
      - uses: actions/checkout@v4

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: ${{ env.DOTNET_VERSION }}

      - name: Restore dependencies
        run: dotnet restore

      - name: Build
        run: dotnet build --no-restore --configuration Release

      - name: Test
        run: dotnet test --no-build --configuration Release --verbosity normal

  build-and-push:
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push'
    name: Build and Push Docker Images

    permissions:
      contents: read
      packages: write

    strategy:
      matrix:
        service: [api, silo]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-${{ matrix.service }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: src/Curios.${{ matrix.service == 'api' && 'Api' || 'Silo' }}/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

  deploy-dev:
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/develop'
    name: Deploy to Development
    environment: development

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Deploy to Development Environment
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.DEV_HOST }}
          username: ${{ secrets.DEV_USERNAME }}
          key: ${{ secrets.DEV_SSH_KEY }}
          script: |
            cd /opt/curios-api-dev

            # Pull latest images
            docker-compose -f docker-compose.yml pull

            # Update and restart development services
            docker-compose -f docker-compose.yml up -d --remove-orphans

            # Clean up old images
            docker image prune -f

            # Run smoke tests for dev environment
            sleep 30
            curl -f http://localhost/weatherforecast || echo "Dev smoke test failed, but continuing..."

  deploy-prod:
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/main'
    name: Deploy to Production
    environment: production

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Deploy to Production
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USERNAME }}
          key: ${{ secrets.PROD_SSH_KEY }}
          script: |
            cd /opt/curios-api-prod

            # Pull latest images
            docker-compose -f docker-compose.yml pull

            # Update and restart services with zero downtime
            docker-compose -f docker-compose.yml up -d --remove-orphans

            # Clean up old images
            docker image prune -f

            # Run smoke tests for production
            sleep 30
            curl -f http://localhost/weatherforecast || echo "Production smoke test failed!"
