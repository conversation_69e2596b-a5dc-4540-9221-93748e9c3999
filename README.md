# 🚀 Curios API

> 基于 .NET 9 + Orleans 9.x 的分布式用户认证和管理系统

## ✨ 特性

- **🏗️ 分布式架构** - 基于 Microsoft Orleans 的可扩展、容错分布式计算
- **🆕 现代 .NET** - 使用 .NET 9 和最新 C# 特性
- **☁️ 云原生** - 支持 Docker 和 Kubernetes 容器化部署
- **📊 可观测性** - 集成日志、指标和分布式追踪
- **🔐 身份认证** - 基于 JWT 的安全用户管理
- **📖 API 优先** - RESTful API 与 OpenAPI/Swagger 文档

## 🏗️ 架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Curios API    │    │  Curios Silo    │    │   基础设施组件   │
│                 │    │                 │    │                 │
│ • REST API      │    │ • Orleans Grain │    │ • PostgreSQL    │
│ • Swagger UI    │    │ • 业务逻辑      │    │ • Redis         │
│ • 健康检查      │    │ • 数据持久化    │    │ • 监控组件      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### **本地开发**
```bash
# 1. 启动基础设施
./scripts/infrastructure.sh local monitoring

# 2. 启动应用程序
./scripts/run-app.sh start local

# 3. 访问应用
# API: http://localhost:5314
# Swagger: http://localhost:5314/swagger
```

### **开发环境部署**
```bash
./scripts/infrastructure.sh development
# 访问: http://localhost
```

### **生产环境部署**
```bash
./scripts/infrastructure.sh production
# 访问: http://localhost
```

## 📚 文档

详细文档请查看 [docs](docs/) 目录：

- **[📚 文档中心](docs/README.md)** - 完整文档索引
- **[🏗️ 基础设施指南](docs/INFRASTRUCTURE_GUIDE.md)** - 组件分离架构和使用方法
- **[🔧 开发环境指南](docs/DEVELOPMENT_GUIDE.md)** - 开发环境搭建流程

## 🛠️ 技术栈

- **.NET 9** - 应用程序框架
- **Orleans 9.x** - 分布式计算框架
- **PostgreSQL** - 数据持久化
- **Redis** - 集群管理和缓存
- **Docker** - 容器化部署
- **Seq + Jaeger + Grafana** - 监控和日志

## 📊 监控面板

| 组件 | 用途 | 访问地址 |
|------|------|----------|
| **API** | 主要接口 | http://localhost:5314 |
| **Swagger** | API 文档 | http://localhost:5314/swagger |
| **Seq** | 日志管理 | http://localhost:5341 |
| **Jaeger** | 分布式追踪 | http://localhost:16686 |
| **Grafana** | 监控面板 | http://localhost:3000 |

## 🧪 测试

```bash
# 运行所有测试
dotnet test

# 构建解决方案
dotnet build

# 恢复包
dotnet restore
```

## 📋 API 端点

- `GET /api/users/{userId}` - 获取用户信息
- `POST /api/users/{userId}` - 更新用户信息
- `PUT /api/users/{userId}/name` - 更新用户名
- `GET /weatherforecast` - 示例天气预报

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
